import csv  # 新增：导入 csv 模块
import logging
import os
from concurrent.futures import ThreadPoolExecutor
import subprocess
import json
import subprocess
from HBB.device import getDevice
import re
from time import sleep
import shlex
import threading  # 新增：用于线程本地存储
import socket  # 新增：用于UDP套接字
import datetime  # 新增：用于日期时间处理
import struct  # 新增：用于数据解析
import traceback  # 新增：用于错误追踪


PATH=os.path.dirname(os.path.abspath(__file__))
TASKRUNNING = 0


# 线程本地存储，用于存储当前任务的taskId
_thread_local = threading.local()

def set_task_id(task_id):
    """设置当前线程的taskId"""
    _thread_local.task_id = task_id

def get_task_id():
    """获取当前线程的taskId"""
    return getattr(_thread_local, 'task_id', None)

def clear_task_id():
    """清除当前线程的taskId"""
    if hasattr(_thread_local, 'task_id'):
        delattr(_thread_local, 'task_id')

# ==================== 日志系统 ====================

class TaskIdFormatter(logging.Formatter):
    """自定义格式化器，支持taskId，与插件统一格式兼容"""

    def __init__(self, base_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'):
        """
        初始化格式化器

        Args:
            base_format: 基础日志格式
        """
        super().__init__(base_format, datefmt='%Y-%m-%d %H:%M:%S')

    def format(self, record):
        # 获取当前线程的taskId
        task_id = get_task_id()

        # 检查消息是否已经包含TaskID前缀，避免重复添加
        original_message = record.getMessage()
        if not original_message.startswith('[TaskID:'):
            if task_id:
                # 如果有taskId，在消息前添加TaskID信息
                record.msg = f"[TaskID: {task_id}] {original_message}"
            else:
                # 如果没有taskId，在消息前添加未知TaskID信息
                record.msg = f"{original_message}"
            record.args = None  # 清除args，避免重复格式化

        return super().format(record)

# 全局 logger 变量，可以被外部设置
logger = None

def init_logger(external_logger=None):
    """
    初始化 logger，支持使用外部传入的 logger 实例

    Args:
        external_logger: 外部传入的 logger 实例（如插件的 logger）
    """
    global logger

    if external_logger:
        # 使用外部传入的统一logger
        logger = external_logger
        #logger.info("Play模块已接入插件统一日志系统")

        # 外部logger已经有TaskID支持，不需要重复添加
        _add_taskid_support_to_logger(logger)
    else:
        # 创建兼容的默认logger配置（保持向后兼容）
        logger = _create_default_logger()
    return logger

def _has_taskid_support(target_logger):
    """
    检查logger是否已经有TaskID支持

    Args:
        target_logger: 目标logger实例

    Returns:
        bool: 如果已经有TaskID支持返回True，否则返回False
    """

    # 检查所有处理器是否都有TaskID支持
    if not target_logger.handlers:
        return False

    for handler in target_logger.handlers:
        



        # 检查格式字符串是否包含TaskID
        if handler.formatter:
            format_str = getattr(handler.formatter, '_fmt', '')
            if 'TaskID' in format_str or '[TaskID:' in format_str:
                continue

        # 如果有任何一个处理器没有TaskID支持，返回False
        return False

    return True

def _add_taskid_support_to_logger(target_logger):
    """
    为现有logger添加TaskID支持，避免重复添加

    Args:
        target_logger: 目标logger实例
    """
    if _has_taskid_support(target_logger):
        return

    # 为所有处理器添加TaskID格式化器
    for handler in target_logger.handlers:
        # 添加TaskID格式化器
        current_formatter = handler.formatter
        original_format = getattr(current_formatter, '_fmt', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        new_formatter = TaskIdFormatter(original_format)
        handler.setFormatter(new_formatter)
    return


def _create_default_logger():
    """
    创建默认的logger配置（向后兼容）

    Returns:
        logging.Logger: 配置好的logger实例
    """
    # 创建默认的 logger 配置，使用统一的logs目录
    logFile = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'play.log')
    os.makedirs(os.path.dirname(logFile), exist_ok=True)

    # 创建自定义 logger 实例
    default_logger = logging.getLogger('plugin.pcapPlay.play')
    default_logger.setLevel(logging.INFO)

    # 禁用向根日志记录器传播，避免重复记录
    default_logger.propagate = False

    # 清除可能存在的处理器，避免重复添加
    if default_logger.handlers:
        default_logger.handlers.clear()

    # 创建统一格式的格式化器
    unified_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    taskIdFormatter = TaskIdFormatter(unified_format)

    # 创建文件处理器，将日志写入文件
    fileHandler = logging.FileHandler(logFile, encoding='utf-8')
    fileHandler.setLevel(logging.DEBUG)
    fileHandler.setFormatter(taskIdFormatter)
    default_logger.addHandler(fileHandler)

    # 创建控制台处理器
    consoleHandler = logging.StreamHandler()
    consoleHandler.setLevel(logging.INFO)
    consoleHandler.setFormatter(taskIdFormatter)
    default_logger.addHandler(consoleHandler)

    default_logger.info("pcapPlay.play模块使用默认日志配置")
    return default_logger





class pcapReplay(object):
    '''报文回放到DUT'''
    def __init__(self, task_id=None):
        self.name = 'pcap_replay'
        self.pacps = []
        self.task_id = task_id
        global logger
        self.logger = init_logger(logger)
        # 如果提供了task_id，设置到线程本地存储
        if task_id:
            set_task_id(task_id)
    
    def rewrite(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']

            # 验证输入文件是否存在
            if not os.path.exists(pcapPath):
                raise FileNotFoundError(f"Input pcap file not found: {pcapPath}")

            dstMac = '00:1C:00:00:00:01'

            # 安全地处理文件路径
            if '.' not in pcapPath:
                self.logger.error(f"Invalid pcap file path format: {pcapPath}")
                return None, None

            pcapName = pcapPath[:pcapPath.rfind('.')]
            cache = pcapName + '.cache'
            outputfile = pcapName + '_out.pcap'
            tempfile= pcapName + '_temp.pcap'
            global PATH
            fragConf=os.path.join(PATH, "uploads", "frag.conf") # 定义报文分片的配置文件，规定了tcprewrite时对报文分片的大小

            
            # 执行tcprewrite命令修复报文小于最低长度字节的问题和报文分片
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum   -i {shlex.quote(pcapPath)} -o {shlex.quote(tempfile)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 执行tcpprep命令
            tcpprep = f'tcpprep -a client -i {shlex.quote(tempfile)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")

                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            # 执行tcprewrite命令
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum   --enet-dmac={dstMac},{dstMac} --endpoints={srcIp}:{dstIp} -i {shlex.quote(tempfile)} -c {shlex.quote(cache)} -o {shlex.quote(pcapPath)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'the second tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(tempfile):
                        try:
                            os.remove(tempfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {tempfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None
            
            # 执行tcprewrite命令,对报文进行分片
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum --fragroute={fragConf} -i {shlex.quote(pcapPath)} -o {shlex.quote(outputfile)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'the second tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(tempfile):
                        try:
                            os.remove(tempfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {tempfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 再次执行tcpprep命令，根据tcprewrite后输出的报文进行重新生成客户端和服务器区分的cache，避免报文分片后cache文件没有更新
            tcpprep = f'tcpprep -a client -i {shlex.quote(outputfile)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(outputfile):
                        try:
                            os.remove(outputfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {outputfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None
            # 清理临时创建的pcap文件
            if os.path.exists(tempfile):
                try:
                    os.remove(tempfile)
                except Exception as cleanup_e:
                    self.logger.error(f"Failed to cleanup pcap file {tempfile}: {str(cleanup_e)}")

            return cache, outputfile

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.rewrite: {str(e)}")
            return None, None

    def replay(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            interface1 = kwargs.get('interface1', '')
            interface2 = kwargs.get('interface2') if kwargs.get('interface2') else interface1

            # 验证网络接口参数
            if not interface1:
                self.logger.error("Network interface1 is required for packet replay")
                return False

            # 调用rewrite方法
            cache, pcapNew = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcapPath)

            # 检查rewrite是否成功
            if cache is None or pcapNew is None:
                self.logger.error("Failed to rewrite pcap file, cannot proceed with replay")
                return False

            # 验证生成的文件是否存在
            if not os.path.exists(cache) or not os.path.exists(pcapNew):
                self.logger.error(f"Required files not found - cache: {cache}, pcapNew: {pcapNew}")
                return False

            # 执行tcpreplay命令
            tcpreplay = f'tcpreplay --loop=1 -c {shlex.quote(cache)} -i {interface1} -I {interface2} {shlex.quote(pcapNew)}'
            try:
                r = subprocess.getstatusoutput(tcpreplay)
                errorMsg=r[1].replace("\n", " ")
                if r[0] != 0:
                    
                    self.logger.error(f'tcpreplay failed with exit code {r[0]}, result: {errorMsg}')
                    return False
                else:
                    self.logger.info(f'Packet replay successful, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcpreplay command: {str(e)}")
                return False
            finally:

                # 清理临时文件
                cleanup_success = True
                if os.path.exists(cache):
                    try:
                        os.remove(cache)
                        self.logger.info(f"Deleted temporary cache file: {cache}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete cache file {cache}: {str(e)}")
                        cleanup_success = False

                if os.path.exists(pcapNew):
                    try:
                        os.remove(pcapNew)
                        self.logger.info(f"Deleted temporary pcap file: {pcapNew}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete pcap file {pcapNew}: {str(e)}")
                        cleanup_success = False

                return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.replay: {str(e)}")
            return False


class dut(object):
    def __init__(self, **kwargs):
        try:
            # 参数验证
            required_params = ['ip', 'user', 'password']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            self.ip = kwargs['ip']
            self.user = kwargs['user']
            self.password = kwargs['password']
            self.task_id = kwargs.get('task_id', None)

            global logger
            self.logger = init_logger(logger)

            # 如果提供了task_id，设置到线程本地存储
            if self.task_id:
                set_task_id(self.task_id)

            # 验证IP地址格式
            import ipaddress
            try:
                ipaddress.ip_address(self.ip)
            except ValueError:
                raise ValueError(f"Invalid IP address format: {self.ip}")

            # 初始化设备连接
            try:
                self.logger.info(f"正在连接到设备: {self.ip}")
                self.dut = getDevice(**kwargs)
                self.logger.info(f"Successfully connected to device: {self.ip}")
            except Exception as e:
                self.logger.error(f"Failed to connect to device {self.ip}: {str(e)}")
                raise

        except Exception as e:
            self.logger.error(f"Error initializing DUT: {str(e)}")
            raise

    def uploadSignature(self, **kwargs):
        try:
            # 参数验证
            if 'sigPath' not in kwargs or 'protocol' not in kwargs:
                raise ValueError("Missing required parameters: sigPath and protocol")

            sigPath = kwargs['sigPath']
            protocol = kwargs['protocol'].lower()

            # 验证签名文件是否存在
            if not os.path.exists(sigPath):
                raise FileNotFoundError(f"Signature file not found: {sigPath}")

            # 读取签名文件并计算行数
            try:
                with open(sigPath, 'r', encoding='utf-8') as f:
                    data = f.readlines()
                    count = len(data)
                self.logger.info(f"Signature file contains {count} lines")
            except Exception as e:
                self.logger.error(f"Failed to read signature file {sigPath}: {str(e)}")
                return False

            # 获取签名ID
            try:
                sigIds = self.getSigId(protocol, count)
                if not sigIds:
                    self.logger.error(f"No signature IDs found for protocol {protocol}")
                    return False
            except Exception as e:
                self.logger.error(f"Failed to get signature IDs: {str(e)}")
                return False

            # 转换签名
            try:
                rule,sigMapping = signatureTransfer(task_id=self.task_id).transfer(sigPath=sigPath, protocol=protocol, sigIds=sigIds)
                if not rule:
                    self.logger.error("Transfer signature failed!")
                    return False
            except Exception as e:
                self.logger.error(f"Failed to transfer signature: {str(e)}")
                return False

            # 设置文件路径
            rulePath = f'/flash/idp/sig/bw_rules/{protocol}.rule'
            ruleBackup = f'/flash/idp/sig/bw_rules/{protocol}backup.rule'
            ruleNamePath=f'/flash/idp/sig/bw_rules/ips_v3.sql'
            ruleNameBackup=f'/flash/idp/sig/bw_rules/ips_v3backup.sql'

            # 备份原始规则文件
            try:
                cmd = f'cp {shlex.quote(rulePath)} {shlex.quote(ruleBackup)}'
                r = self.dut.shell(cmd)
                cmd = f'cp {shlex.quote(ruleNamePath)} {shlex.quote(ruleNameBackup)}'
                r = self.dut.shell(cmd)
                sleep(1)
                self.logger.info(f"Backed up original rule file to {ruleBackup};{ruleNameBackup}")
            except Exception as e:
                self.logger.error(f"Failed to backup rule file: {str(e)}")
                return False

            # 清空规则文件
            try:
                cmd = f'echo "" > {shlex.quote(rulePath)}'
                r = self.dut.shell(cmd)
                self.logger.info(f"Cleared rule file: {rulePath}")
            except Exception as e:
                self.logger.error(f"Failed to clear rule file: {str(e)}")
                return False

            # 写入新规则
            try:
                for line in rule[protocol].split('\n'):
                    if line.strip():  # 跳过空行
                        cmd = f'echo {shlex.quote(line)} >> {shlex.quote(rulePath)}'
                        r = self.dut.shell(cmd)
                self.logger.info(f"Successfully wrote new rules to {rulePath}")
                protocolId=self.getProtocolId(protocol)
                for sigId in sigMapping.keys():
                    sigName = sigMapping[sigId]
                    #cmd = f'sed -i -E "s/({protocolId},{sigId},)[^,]*,[^,]*/\\1{sigName},{sigName}/" "{ruleNamePath}"'
                    cmd = f'sed -i "s/INSERT INTO \\"sig_infos\\" VALUES({protocolId},{sigId},[^,]*,[^,]*/INSERT INTO \\"sig_infos\\" VALUES({protocolId},{sigId},{shlex.quote(sigName)},{shlex.quote(sigName)}/" "{ruleNamePath}"'
                    r = self.dut.shell(cmd)
            except Exception as e:
                self.logger.error(f"Failed to write new rules: {str(e)}")
                # 尝试恢复备份
                try:
                    restore_cmd = f'cp {shlex.quote(ruleBackup)} {shlex.quote(rulePath)}'
                    self.dut.shell(restore_cmd)
                    restore_cmd = f'cp {shlex.quote(ruleNameBackup)} {shlex.quote(ruleNamePath)}'
                    self.dut.shell(restore_cmd)
                    self.logger.info("Restored backup rule file")
                except:
                    self.logger.error("Failed to restore backup rule file")
                return False

            # 重新加载签名
            try:
                r = self.reloadSig()
                if not r:
                    self.logger.error("Reload signature failed!")
                    return False
                self.logger.info("Successfully reloaded signatures")
            except Exception as e:
                self.logger.error(f"Failed to reload signatures: {str(e)}")
                return False

            # 恢复原始规则文件
            try:
                cmd = f'cp {shlex.quote(ruleBackup)} {shlex.quote(rulePath)}'
                r = self.dut.shell(cmd)
                restore_cmd = f'cp {shlex.quote(ruleNameBackup)} {shlex.quote(ruleNamePath)}'
                self.dut.shell(restore_cmd)
                sleep(1)
                self.dut.cli('')
                self.logger.info("Restored original rule file")
            except Exception as e:
                self.logger.error(f"Failed to restore original rule file: {str(e)}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error in uploadSignature: {str(e)}")
            return False

    def reloadSig(self):
        try:
            cmd = 'exec ips-reload'
            self.logger.info("Starting IPS signature reload...")

            try:
                self.dut.cli(cmd)
                sleep(5)
                self.logger.info("IPS reload command executed, waiting for engine to start...")
            except Exception as e:
                self.logger.error(f"Failed to execute IPS reload command: {str(e)}")
                return False

            # 等待IPS引擎启动，最多等待5分钟
            for attempt in range(60):
                try:
                    ipstatus = self.getIpsStatus()
                    if 'engineStatus' in ipstatus:
                        if ipstatus['engineStatus'] == 'started':
                            self.logger.info(f"IPS engine started successfully after {(attempt + 1) * 5} seconds")
                            return True
                        else:
                            self.logger.info(f"IPS engine status: {ipstatus['engineStatus']}, waiting... (attempt {attempt + 1}/60)")
                    else:
                        self.logger.warning(f"Unable to get engine status, attempt {attempt + 1}/60")

                    sleep(5)

                except Exception as e:
                    self.logger.error(f"Error checking IPS status on attempt {attempt + 1}: {str(e)}")
                    sleep(5)

            self.logger.error("IPS engine failed to start within 5 minutes")
            return False

        except Exception as e:
            self.logger.error(f"Error in reloadSig: {str(e)}")
            return False

    
    def getIpsStatus(self):
        try:
            status = {}
            cmd = 'show ips status'

            try:
                result = self.dut.cli(cmd)
                if not result:
                    self.logger.warning("Empty result from IPS status command")
                    return status
            except Exception as e:
                self.logger.error(f"Failed to execute IPS status command: {str(e)}")
                return status

            # 解析IPS状态
            try:
                pattern = re.compile(
                    r"IPS engine is (\w+)\s*"
                    r"(?:.*\n)*?"
                    r"IPS feature:\s*(\w+)\s*"
                    r"(?:.*Engine version:\s*(\d+))?\s*"
                    r"(?:.*IPS magic:\s*([0-9a-fA-F]+))?",
                    re.MULTILINE
                )

                match = pattern.search(result)
                if match:
                    status['engineStatus'] = match.group(1)
                    status['featureStatus'] = match.group(2)
                    status['engineVersion'] = match.group(3) or "N/A"
                    status['magic'] = match.group(4) or "N/A"
                    self.logger.debug(f"Parsed IPS status: {status}")
                else:
                    self.logger.warning(f"Failed to parse IPS status from output: {result[:200]}...")

            except Exception as e:
                self.logger.error(f"Error parsing IPS status: {str(e)}")

            return status

        except Exception as e:
            self.logger.error(f"Error in getIpsStatus: {str(e)}")
            return {}




    def getSigId(self, protocol, count):
        try:
            sigIds = []
            protocol_lower = protocol.lower()

            # 构建命令
            cmd = f'sed -n "/idp_signature/p" /flash/idp/sig/bw_rules/{protocol_lower}.rule | head -{count}'

            try:
                result = self.dut.shell(cmd)
                self.dut.cli('')

                if not result:
                    self.logger.warning(f"No signature IDs found for protocol {protocol}")
                    return sigIds

            except Exception as e:
                self.logger.error(f"Failed to execute getSigId command: {str(e)}")
                return sigIds

            # 解析签名ID
            try:
                pattern = re.compile(r"idp_signature\s+(\d+):%s"%protocol, re.IGNORECASE)
                for line in result.split('\n'):
                    line = line.strip()
                    if line:
                        match = pattern.match(line)
                        if match:
                            sigIds.append(match.group(1))

                self.logger.info(f"Found {len(sigIds)} signature IDs for protocol {protocol}")

            except Exception as e:
                self.logger.error(f"Error parsing signature IDs: {str(e)}")

            return sigIds

        except Exception as e:
            self.logger.error(f"Error in getSigId: {str(e)}")
            return []

    def getProtocolId(self, protocol):
        mapping = {
            "DNS": "1", "FTP": "2", "HTTP": "3", "POP3": "4", "SMTP": "5", "TELNET": "6",
            "TCP": "7", "UDP": "8", "IMAP": "9", "FINGER": "10", "SUNRPC": "11", "NNTP": "12",
            "TFTP": "13", "SNMP": "14", "MYSQL": "15", "MSSQL": "16", "ORACLE": "17",
            "MSRPC": "18", "NETBIOS": "19", "DHCP": "20", "LDAP": "21", "VOIP": "22"
        }
        protocol = protocol.upper()
        if protocol in mapping.keys():
            return mapping[protocol]
        else:
            return None

    def getLog(self):
        try:
            cmd = 'show logging threat'
            result = self.dut.cli(cmd)
            if result is None:
                self.logger.warning("No threat log data received")
                return ""
            return result
        except Exception as e:
            self.logger.error(f"Error getting threat log: {str(e)}")
            return ""
        

    def clearLog(self):
        try:
            cmd = 'clear logging threat'
            self.dut.cli(cmd)
            self.logger.info("Threat log cleared successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error clearing threat log: {str(e)}")
            return False

    def clearSession(self,srcIp,dstIp):
        try:
            cmd = 'clear session src-ip %s'%srcIp
            self.dut.cli(cmd)
            cmd = 'clear session src-ip %s'%dstIp
            self.dut.cli(cmd)
            self.logger.info("Sessions cleared successfully")

            #try:
            #    self.dut.reconnect()
            #     self.logger.info("Device reconnected successfully")
            #except Exception as e:
            #    self.logger.error(f"Failed to reconnect after clearing sessions: {str(e)}")
            #    return False

            return True
        except Exception as e:
            self.logger.error(f"Error clearing sessions: {str(e)}")
            return False

    def checkLog(self,srcIp, dstIp):
        sigName=[]
        try:
            # 验证IP地址参数
            if not srcIp or not dstIp:
                self.logger.error("Source IP and destination IP are required")
                return False,sigName, ""

            # 获取威胁日志
            threatLog = self.getLog()

            if not threatLog:
                self.logger.warning("No threat log data available")
                return False,sigName, ""

            # 转义IP地址中的特殊字符
            try:
                srcIp_escaped = re.escape(srcIp)
                dstIp_escaped = re.escape(dstIp)
                pattern = re.compile(f"({srcIp_escaped}.*?{dstIp_escaped}|{dstIp_escaped}.*?{srcIp_escaped}).*?threat name:(.*?),")
                results = pattern.findall(threatLog)
                

                if results:
                    self.logger.info(f"Found matching threat log for {srcIp} <-> {dstIp}")
                    for r in results:
                        sigName.append(r[1].strip())
                    return True, sigName,threatLog
                else:
                    self.logger.info(f"No matching threat log found for {srcIp} <-> {dstIp}")
                    return False,sigName, threatLog

            except Exception as e:
                self.logger.error(f"Error parsing threat log: {str(e)}")
                return False,sigName, threatLog

        except Exception as e:
            self.logger.error(f"Error in checkLog: {str(e)}")
            return False, sigName,""

    def config(self, cmd):
        return self.dut.config(cmd)



class pcapTest(object):
    def __init__(self, **kwargs):
        self.taskId = kwargs.get('taskId', '')
        self.taskName = kwargs.get('taskName', '')  # 新增：用于存储任务名称
        self.dut = None
        # 设置taskId到线程本地存储
        if self.taskId:
            set_task_id(self.taskId)

        global logger
        self.logger = init_logger(logger)

        

        # 记录初始化日志
        self.logger.info(f"pcapTest实例初始化完成 - 任务名称: {self.taskName}")

    def getReachableLocalIp(self, target_ip):
        """
        获取与目标IP能够互通的本机IP地址

        Args:
            target_ip (str): 目标IP地址

        Returns:
            str: 能够与目标IP互通的本机IP地址，如果没有找到返回None
        """
        try:
            # 验证输入IP格式
            import ipaddress
            try:
                ipaddress.ip_address(target_ip)
            except ValueError:
                self.logger.error(f"Invalid IP address format: {target_ip}")
                return None

            self.logger.info(f"正在查找与目标IP {target_ip} 互通的本机IP地址...")

            # 方法1: 使用socket连接测试（最准确的方法）
            local_ip = self._getLocalIpBySocket(target_ip)
            if local_ip:
                self.logger.info(f"通过socket方法找到本机IP: {local_ip} -> {target_ip}")
                return local_ip

            # 方法2: 通过路由表查找
            local_ip = self._getLocalIpByRoute(target_ip)
            if local_ip:
                self.logger.info(f"通过路由表找到本机IP: {local_ip} -> {target_ip}")
                return local_ip

            # 方法3: 通过网络接口匹配
            local_ip = self._getLocalIpByInterface(target_ip)
            if local_ip:
                self.logger.info(f"通过接口匹配找到本机IP: {local_ip} -> {target_ip}")
                return local_ip

            self.logger.warning(f"未找到与目标IP {target_ip} 互通的本机IP")
            return None

        except Exception as e:
            self.logger.error(f"获取本机IP失败: {str(e)}")
            return None

    def _getLocalIpBySocket(self, target_ip):
        """
        通过socket连接获取本机IP（最准确的方法）

        Args:
            target_ip (str): 目标IP地址

        Returns:
            str: 本机IP地址
        """
        try:
            # 创建UDP socket连接到目标IP
            # 使用UDP避免实际建立连接，只是确定路由
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                # 连接到目标IP的任意端口（这里使用80）
                s.connect((target_ip, 80))
                # 获取本地socket绑定的IP地址
                local_ip = s.getsockname()[0]

                # 验证获取的IP不是回环地址
                if local_ip and local_ip != '127.0.0.1':
                    return local_ip

        except Exception as e:
            self.logger.debug(f"Socket方法获取本机IP失败: {str(e)}")

        return None

    def _getLocalIpByRoute(self, target_ip):
        """
        通过路由表获取本机IP

        Args:
            target_ip (str): 目标IP地址

        Returns:
            str: 本机IP地址
        """
        try:
            # 使用ip route命令查找路由
            cmd = f"ip route get {target_ip}"
            result = subprocess.getstatusoutput(cmd)

            if result[0] == 0:
                output = result[1].strip()
                self.logger.debug(f"路由查询结果: {output}")

                # 解析输出，查找src字段
                # 示例输出: ************* via *********** dev eth0 src ***********0 uid 1000
                src_match = re.search(r'src\s+(\d+\.\d+\.\d+\.\d+)', output)
                if src_match:
                    return src_match.group(1)

                # 如果没有src字段，尝试解析dev字段对应的接口IP
                dev_match = re.search(r'dev\s+(\w+)', output)
                if dev_match:
                    interface = dev_match.group(1)
                    return self._getInterfaceIp(interface)

        except Exception as e:
            self.logger.debug(f"路由表方法获取本机IP失败: {str(e)}")

        return None

    def _getLocalIpByInterface(self, target_ip):
        """
        通过网络接口匹配获取本机IP

        Args:
            target_ip (str): 目标IP地址

        Returns:
            str: 本机IP地址
        """
        try:
            import ipaddress
            target_addr = ipaddress.ip_address(target_ip)

            # 获取所有网络接口信息
            interfaces_info = self._getAllInterfacesInfo()

            best_match = None
            best_prefix_len = -1

            for interface_info in interfaces_info:
                try:
                    ip = interface_info['ip']
                    netmask = interface_info['netmask']

                    if ip and netmask and ip != '127.0.0.1':
                        # 创建网络对象
                        network = ipaddress.IPv4Network(f"{ip}/{netmask}", strict=False)

                        # 检查目标IP是否在这个网络中
                        if target_addr in network:
                            # 找到直接连接的网络，优先级最高
                            if network.prefixlen > best_prefix_len:
                                best_match = ip
                                best_prefix_len = network.prefixlen

                except Exception as e:
                    self.logger.debug(f"处理接口信息失败: {str(e)}")
                    continue

            return best_match

        except Exception as e:
            self.logger.debug(f"接口匹配方法获取本机IP失败: {str(e)}")

        return None

    def _getInterfaceIp(self, interface_name):
        """
        获取指定网络接口的IP地址

        Args:
            interface_name (str): 网络接口名称

        Returns:
            str: IP地址
        """
        try:
            # 使用ip命令获取接口IP
            cmd = f"ip addr show {interface_name}"
            result = subprocess.getstatusoutput(cmd)

            if result[0] == 0:
                output = result[1]
                # 查找inet地址
                inet_match = re.search(r'inet\s+(\d+\.\d+\.\d+\.\d+)', output)
                if inet_match:
                    ip = inet_match.group(1)
                    if ip != '127.0.0.1':
                        return ip

        except Exception as e:
            self.logger.debug(f"获取接口 {interface_name} IP失败: {str(e)}")

        return None

    def _getAllInterfacesInfo(self):
        """
        获取所有网络接口信息

        Returns:
            list: 接口信息列表，每个元素包含interface、ip、netmask等信息
        """
        interfaces_info = []

        try:
            # 使用ip命令获取所有接口信息
            cmd = "ip addr show"
            result = subprocess.getstatusoutput(cmd)

            if result[0] == 0:
                output = result[1]

                # 解析接口信息
                interface_blocks = re.split(r'\n(?=\d+:)', output)

                for block in interface_blocks:
                    if not block.strip():
                        continue

                    try:
                        # 提取接口名称
                        interface_match = re.search(r'\d+:\s*([^:@]+)', block)
                        if not interface_match:
                            continue

                        interface_name = interface_match.group(1).strip()

                        # 跳过回环接口
                        if interface_name.startswith('lo'):
                            continue

                        # 提取IP地址和子网掩码
                        inet_matches = re.findall(r'inet\s+(\d+\.\d+\.\d+\.\d+)/(\d+)', block)

                        for ip, prefix_len in inet_matches:
                            if ip != '127.0.0.1':
                                # 将前缀长度转换为子网掩码
                                netmask = self._prefixToNetmask(int(prefix_len))

                                interfaces_info.append({
                                    'interface': interface_name,
                                    'ip': ip,
                                    'netmask': netmask,
                                    'prefix_len': int(prefix_len)
                                })

                    except Exception as e:
                        self.logger.debug(f"解析接口信息失败: {str(e)}")
                        continue

        except Exception as e:
            self.logger.debug(f"获取接口信息失败: {str(e)}")

        return interfaces_info

    def _prefixToNetmask(self, prefix_len):
        """
        将前缀长度转换为子网掩码

        Args:
            prefix_len (int): 前缀长度

        Returns:
            str: 子网掩码
        """
        try:
            # 创建掩码
            mask = (0xffffffff >> (32 - prefix_len)) << (32 - prefix_len)

            # 转换为点分十进制
            return f"{(mask >> 24) & 0xff}.{(mask >> 16) & 0xff}.{(mask >> 8) & 0xff}.{mask & 0xff}"

        except Exception as e:
            self.logger.debug(f"转换子网掩码失败: {str(e)}")
            return "*************"  # 默认值

    def testConnectivity(self, local_ip, target_ip, timeout=3):
        """
        测试从指定本机IP到目标IP的连通性

        Args:
            local_ip (str): 本机IP地址
            target_ip (str): 目标IP地址
            timeout (int): 超时时间（秒）

        Returns:
            bool: 是否能够连通
        """
        try:
            # 使用ping命令测试连通性
            cmd = f"ping -c 1 -W {timeout} -I {local_ip} {target_ip}"
            result = subprocess.getstatusoutput(cmd)

            success = result[0] == 0
            if success:
                self.logger.info(f"连通性测试成功: {local_ip} -> {target_ip}")
            else:
                self.logger.debug(f"连通性测试失败: {local_ip} -> {target_ip}, 错误: {result[1]}")

            return success

        except Exception as e:
            self.logger.debug(f"连通性测试异常: {str(e)}")
            return False

    def getAllLocalIps(self):
        """
        获取所有本机IP地址

        Returns:
            list: 本机IP地址信息列表
        """
        try:
            interfaces_info = self._getAllInterfacesInfo()
            self.logger.info(f"找到 {len(interfaces_info)} 个网络接口")

            for info in interfaces_info:
                self.logger.info(f"接口 {info['interface']}: {info['ip']}/{info['prefix_len']}")

            return interfaces_info

        except Exception as e:
            self.logger.error(f"获取所有本机IP失败: {str(e)}")
            return []

    def _cleanCsvData(self, data):
        """
        清理CSV数据，移除或转义可能导致问题的字符

        Args:
            data (str): 原始数据

        Returns:
            str: 清理后的数据
        """
        try:
            if data is None:
                return ''

            # 转换为字符串
            data_str = str(data)

            # 移除控制字符（除了常见的空白字符）
            import re
            # 保留常见的空白字符，移除其他控制字符
            data_str = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', data_str)

            # 替换可能有问题的字符
            #data_str = data_str.replace('\r\n', ' ')  # 替换Windows换行符
            #data_str = data_str.replace('\n', ' ')    # 替换Unix换行符
            #data_str = data_str.replace('\r', ' ')    # 替换Mac换行符
            #data_str = data_str.replace('\t', ' ')    # 替换制表符

            # 限制长度，避免过长的数据
            #if len(data_str) > 1000:
            #    data_str = data_str[:997] + '...'

            # 去除首尾空白
            data_str = data_str.strip()

            return data_str

        except Exception as e:
            self.logger.debug(f"Error cleaning CSV data: {str(e)}")
            return str(data)[:100] if data else ''

    def _generate_ip_address(self, base_ip, increment):
        """
        生成IP地址，支持正确的自增长

        Args:
            base_ip (str): 基础IP地址，如 '*******'
            increment (int): 增长数值

        Returns:
            str: 生成的IP地址

        Examples:
            _generate_ip_address('*******', 0) -> '*******'
            _generate_ip_address('*******', 255) -> '*********'
            _generate_ip_address('*******', 256) -> '*******'
            _generate_ip_address('*******', 65536) -> '*******'
        """
        try:
            # 将IP地址转换为整数
            parts = base_ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"Invalid IP address format: {base_ip}")

            # 验证每个部分都是有效的0-255范围
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    raise ValueError(f"Invalid IP address part: {part}")

            # 将IP地址转换为32位整数
            ip_int = (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

            # 加上增长值
            new_ip_int = ip_int + increment

            # 确保不超过32位整数的最大值（避免溢出）
            if new_ip_int > 0xFFFFFFFF:
                new_ip_int = 0xFFFFFFFF

            # 将整数转换回IP地址
            new_parts = [
                (new_ip_int >> 24) & 0xFF,
                (new_ip_int >> 16) & 0xFF,
                (new_ip_int >> 8) & 0xFF,
                new_ip_int & 0xFF
            ]

            return '.'.join(map(str, new_parts))

        except Exception as e:
            self.logger.error(f"Error generating IP address from {base_ip} with increment {increment}: {str(e)}")
            # 如果出错，返回基础IP地址
            return base_ip

    def _validate_pcap_format(self, pcap_path):
        """
        验证pcap文件格式的基本有效性
        """
        try:
            with open(pcap_path, 'rb') as f:
                # 读取文件头部
                header = f.read(24)

                if len(header) < 24:
                    self.logger.warning(f"Pcap file too small: {pcap_path}")
                    return False

                # 检查pcap文件魔数
                magic_numbers = [
                    b'\xd4\xc3\xb2\xa1',  # 标准pcap (little endian)
                    b'\xa1\xb2\xc3\xd4',  # 标准pcap (big endian)
                    b'\x0a\x0d\x0d\x0a',  # pcapng
                    b'\x4d\x3c\xb2\xa1',  # 修改的pcap
                    b'\xa1\xb2\x3c\x4d'   # 修改的pcap (big endian)
                ]

                file_magic = header[:4]
                if file_magic not in magic_numbers:
                    self.logger.warning(f"Invalid pcap magic number in file: {pcap_path}")
                    return False

                # 对于pcap文件，尝试读取第一个数据包头部
                if file_magic in [b'\xd4\xc3\xb2\xa1', b'\xa1\xb2\xc3\xd4']:
                    # 跳过全局头部，尝试读取第一个数据包记录头部
                    packet_header = f.read(16)
                    if len(packet_header) < 16:
                        self.logger.warning(f"No packet data found in pcap file: {pcap_path}")
                        return False

                    # 解析数据包长度
                    if file_magic == b'\xd4\xc3\xb2\xa1':  # little endian
                        import struct
                        _, _, caplen, _ = struct.unpack('<IIII', packet_header)
                    else:  # big endian
                        import struct
                        _, _, caplen, _ = struct.unpack('>IIII', packet_header)

                    # 检查数据包长度是否合理
                    if caplen == 0 or caplen > 65535:
                        self.logger.warning(f"Invalid packet length ({caplen}) in pcap file: {pcap_path}")
                        return False

                self.logger.info(f"Pcap file format validation passed: {pcap_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error validating pcap format for {pcap_path}: {str(e)}")
            return False

    def getSyslog(self):
        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
        log_filename = f"syslog_{current_date}.log"
        log_filepath = os.path.join(PATH, 'logs', 'syslog', log_filename)
        if os.path.exists(log_filepath):
            with open(log_filepath, 'r') as file:
                log_data = file.read()
                return log_data
        else:
            return ""
    
    def checkSyslog(self,srcIp,dstIp):
        sigName=[]
        syslog=[]
        try:
            # 验证IP地址参数
            if not srcIp or not dstIp:
                self.logger.error("Source IP and destination IP are required")
                return False,sigName,syslog

            # 获取威胁日志
            threatLog = self.getSyslog()

            if not threatLog:
                self.logger.warning("No threat log data available")
                return False,sigName,syslog   

            # 转义IP地址中的特殊字符
            try:
                srcIp_escaped = re.escape(srcIp)
                dstIp_escaped = re.escape(dstIp)
                #pattern = re.compile(f"\n(.*?({srcIp_escaped}.*?{dstIp_escaped}|{dstIp_escaped}.*?{srcIp_escaped}).*?threat name:(.*?),.*)\n")
                pattern = re.compile(rf"(^(?=.*{srcIp_escaped})(?=.*{dstIp_escaped}).*?threat name:\s*(.*?),.*$)",re.MULTILINE)
                results = pattern.findall(threatLog)
                

                if results:
                    self.logger.info(f"Found matching threat log for {srcIp} <-> {dstIp}")
                    for r in results:
                        sigName.append(r[1].strip())
                        syslog.append(r[0].strip())
                    return True, sigName,syslog
                else:
                    self.logger.info(f"No matching threat log found for {srcIp} <-> {dstIp}")
                    return False,sigName, syslog

            except Exception as e:
                self.logger.error(f"Error parsing threat log: {str(e)}")
                return False,sigName, syslog

        except Exception as e:
            self.logger.error(f"Error in checkLog: {str(e)}")
            return False, sigName,syslog

    
    def clearSyslog(self):
        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
        log_filename = f"syslog_{current_date}.log"
        log_filepath = os.path.join(PATH, 'logs', 'syslog', log_filename)
        if os.path.exists(log_filepath):
            with open(log_filepath, 'w') as file:
                file.write("")
        return

    def preTest(self, **kwargs):
        '''预处理，获取被测设备信息，测试输入的报文文件'''
        try:

            global syslogServer
            syslogServer.start()
            # 参数验证
            if 'pcapPaths' not in kwargs:
                raise ValueError("Missing required parameter: pcapPaths")

            # 读取设备配置
            from .plugin import Plugin
            config=Plugin().config
            device=config['device']
            replay=config['replay']

            try:
                
                # 初始化设备连接
                try:
                    self.dut = dut(
                        ip=device['ip'],
                        user=device['username'],
                        password=device['password'],
                        os='stoneos',
                        task_id=self.taskId  # 传递taskId
                    )
                    self.logger.info(f"Device connection initialized: {device['ip']}")
                except Exception as e:
                    self.logger.error(f"Failed to initialize device connection: {str(e)}")
                    raise
                #设置DUT日志外发服务器
                try:
                    # 获取与DUT设备互通的本机IP地址
                    self.pcIp = self.getReachableLocalIp(device['ip'])
                    if self.pcIp:
                        self.logger.info(f"找到与DUT设备 {device['ip']} 互通的本机IP: {self.pcIp}")
                        cmd='logging syslog %s udp 514 type threat'%self.pcIp
                        self.dut.config(cmd)
                        cmd='logging  threat to syslog'
                        self.dut.config(cmd)
                        self.logger.info(f"已配置DUT日志外发到本机IP: {self.pcIp}")
                    else:
                        self.logger.error(f"未找到与DUT设备 {device['ip']} 互通的本机IP，跳过日志外发配置")
                    #取消被测设备上的日志聚合设置
                    cmds=['no ips log aggregation','no av  agg-log enable','no botnet-c2-prevention agg-log enable']
                    for cmd in cmds:
                        self.dut.config(cmd)

                except Exception as e:
                    self.logger.error(f"被测设备日志外发配置失败，跳过日志外发配置")
                    raise

                # 设置网络接口
                self.interface1 = replay.get('interface1', '')
                self.interface2 = replay.get('interface2', self.interface1)

                if not self.interface1:
                    self.logger.warning("No network interface specified in device config")

            except json.JSONDecodeError as e:
                self.logger.error(f"Invalid JSON in device config file: {str(e)}")
                raise
            except Exception as e:
                self.logger.error(f"Error reading device config: {str(e)}")
                raise

            # 验证输入文件
            self.pcapPaths = kwargs['pcapPaths']

            # 验证pcap文件
            if not self.pcapPaths:
                raise ValueError("No pcap files provided")

            # 验证并清理pcap文件路径
            valid_pcap_paths = []
            for pcap_path in self.pcapPaths:
                try:
                    # 规范化路径
                    normalized_path = os.path.normpath(pcap_path)

                    if not os.path.exists(normalized_path):
                        self.logger.error(f"Pcap file not found: {normalized_path}")
                        continue

                    # 检查文件是否可读
                    if not os.access(normalized_path, os.R_OK):
                        self.logger.error(f"Pcap file not readable: {normalized_path}")
                        continue

                    # 检查文件大小
                    file_size = os.path.getsize(normalized_path)
                    if file_size == 0:
                        self.logger.error(f"Pcap file is empty: {normalized_path}")
                        continue

                    # 检查pcap文件格式的基本有效性
                    if not self._validate_pcap_format(normalized_path):
                        self.logger.error(f"Invalid pcap file format: {normalized_path}")
                        continue

                    valid_pcap_paths.append(normalized_path)
                    self.logger.info(f"Validated pcap file: {normalized_path} ({file_size} bytes)")

                except Exception as e:
                    self.logger.error(f"Error validating pcap file {pcap_path}: {str(e)}")
                    continue

            if not valid_pcap_paths:
                raise ValueError("No valid pcap files found after validation")

            # 更新pcap路径列表为验证后的路径
            self.pcapPaths = valid_pcap_paths

            self.logger.info(f"Found {len(self.pcapPaths)} valid pcap files for testing")
            self.logger.info("Skipping signature upload - testing pcap files directly")

            return True

        except Exception as e:
            self.logger.error(f"Error in preTest: {str(e)}")
            raise
    

    def mainTest(self, **kwargs):
        self.taskId = kwargs['taskId']
        self.taskName = kwargs.get('taskName', '')
        pcapPaths=kwargs['pcapPaths']

        # 设置线程本地的TaskID上下文，确保日志中显示TaskID
        set_task_id(self.taskId)

        # 移除sigPath和protocol参数，直接进行pcap文件测试

        global TASKRUNNING
        for n in range(10):
            if not TASKRUNNING:
                TASKRUNNING = 1
                break
            else:
                sleep(60)
            if n == 9:
                # 任务失败告警
                error_msg = f"任务 {self.taskName} (ID: {self.taskId}) 执行失败: 当前存在任务还在长时间运行测试中，请稍后再试。"
                self.logger.error(error_msg)
                from .plugin import Plugin,TASK_FINISH,TASK_FAIL
                Plugin().updateTask(self.taskId,TASK_FINISH,TASK_FAIL,'')
                return

        try:
            # 告警处理由调用方（插件）处理
            id=get_task_id()
            self.logger.info(f"当前线程的task id：{id}")
            self.logger.info(f"开始执行任务 {self.taskName} (ID: {self.taskId})")

            results = {}
            resultSummary={}
            resultDetail = {}

            # 执行预处理
            try:
                self.preTest(pcapPaths=pcapPaths, taskId=self.taskId, taskName=self.taskName)
            except Exception as e:
                self.logger.error(f"PreTest failed: {str(e)}")
                raise

            # 执行测试
            count = 1
            total_files = len(self.pcapPaths)
            self.logger.info(f" Starting test execution for {total_files} pcap files")

            for pcap in self.pcapPaths:
                try:
                    # 验证文件路径
                    if not pcap or not os.path.exists(pcap):
                        self.logger.error(f" Invalid pcap file path: {pcap}")
                        results[f"invalid_file_{count}"] = False
                        resultSummary[f"invalid_file_{count}"] = []
                        resultDetail[f"invalid_file_{count}"] = f"Invalid file path: {pcap}"
                        count += 1
                        continue

                    # 使用IP地址自增长函数，避免生成非法IP地址
                    srcIp = self._generate_ip_address('*********', count - 1)
                    dstIp = self._generate_ip_address('*********', count - 1)
                    pcapName = os.path.basename(pcap)

                    self.logger.info(f" Testing pcap file {count}/{total_files}: {pcapName}")
                    self.logger.info(f" File path: {pcap}")
                    self.logger.info(f" Using srcIp: {srcIp}, dstIp: {dstIp}")

                    # 清理日志和会话
                    try:
                        if not self.dut.clearLog():
                            self.logger.warning(f"Failed to clear log for {pcapName}")
                        if not self.dut.clearSession(srcIp,dstIp):
                            self.logger.warning(f"Failed to clear session for {pcapName}")
                    except Exception as e:
                        self.logger.error(f"Error clearing logs/sessions for {pcapName}: {str(e)}")
                        # 继续执行，不中断测试

                    # 执行报文回放
                    try:
                        replay_result = pcapReplay(task_id=self.taskId).replay(
                            pcapPath=pcap,
                            srcIp=srcIp,
                            dstIp=dstIp,
                            interface1=self.interface1,
                            interface2=self.interface2
                        )
                        if not replay_result:
                            self.logger.error(f"Packet replay failed for {pcapName}")
                            results[pcapName] = False
                            resultSummary[pcapName]=[]
                            resultDetail[pcapName] = "Packet replay failed"
                            count += 1
                            continue
                    except Exception as e:
                        self.logger.error(f"Error during packet replay for {pcapName}: {str(e)}")
                        results[pcapName] = False
                        resultSummary[pcapName] = []
                        resultDetail[pcapName] = f"Packet replay error: {str(e)}"
                        count += 1
                        continue

                    # 等待处理完成
                    sleep(1)

                    # 检查日志
                    try:
                        result = self.checkSyslog(srcIp=srcIp, dstIp=dstIp)
                        results[pcapName] = result[0]
                        resultSummary[pcapName] = result[1]
                        resultDetail[pcapName] = result[2]

                        status = "DETECTED" if result[0] else "NOT DETECTED"
                        self.logger.info(f"Test result for {pcapName}: {status}")

                    except Exception as e:
                        self.logger.error(f"Error checking log for {pcapName}: {str(e)}")
                        results[pcapName] = False
                        resultSummary[pcapName]=[]
                        resultDetail[pcapName] = [f"Log check error: {str(e)}"]

                    count += 1

                except Exception as e:
                    pcap_name = os.path.basename(pcap) if pcap else f"file_{count}"
                    self.logger.error(f" Error processing pcap file {pcap_name}: {str(e)}")
                    self.logger.error(f" Error details: {traceback.format_exc()}")
                    results[pcap_name] = False
                    resultSummary[pcap_name] = []
                    resultDetail[pcap_name] = [f"Processing error: {str(e)}"]
                    count += 1
                    continue

            # 生成报告
            try:
                reportPath = self.report(results=results, resultSummary=resultSummary,resultDetail=resultDetail)
                self.logger.info(f"Test report generated: {reportPath}")
            except Exception as e:
                self.logger.error(f"Failed to generate report: {str(e)}")
                reportPath = ""

            # 执行后处理
            try:
                self.afterTest(pcapPaths=pcapPaths, taskId=self.taskId)
            except Exception as e:
                self.logger.error(f"AfterTest failed: {str(e)}")
                # 不抛出异常，因为主要测试已完成

            # 任务状态更新由调用方（插件）处理
            self.logger.info(f"任务 {self.taskName} (ID: {self.taskId}) 执行成功，报告路径: {reportPath}")
            from .plugin import Plugin,TASK_FINISH,TASK_SUCCESS
            Plugin().updateTask(self.taskId,TASK_FINISH,TASK_SUCCESS,reportPath)


            # 任务完成，不需要发送告警（告警系统只用于错误信息）

        except Exception as e:
            # 新增：任务失败告警
            error_msg = f"任务 {self.taskName} (ID: {self.taskId}) 执行失败: {str(e)}"
            self.logger.error(error_msg)
            from .plugin import Plugin,TASK_FINISH,TASK_FAIL
            Plugin().updateTask(self.taskId,TASK_FINISH,TASK_FAIL,'')
            # 任务状态更新由调用方（插件）处理
        TASKRUNNING = 0

        # 清理线程本地的TaskID上下文
        clear_task_id()
        return

    def afterTest(self, **kwargs):
        try:
            # 参数验证
            if 'pcapPaths' not in kwargs:
                self.logger.warning("Missing pcapPaths parameter in afterTest, skipping cleanup")
                return False

            pcapPaths = kwargs['pcapPaths']
            taskId = kwargs.get('taskId', 'unknown')

            # 设置线程本地的TaskID上下文，确保日志中显示TaskID
            set_task_id(taskId)

            cleanup_success = True

            # 检查是否为文件夹上传模式
            is_folder_upload = self._is_folder_upload_mode(pcapPaths, taskId)

            if is_folder_upload:
                # 文件夹上传模式：删除整个任务目录
                cleanup_success = self._cleanup_folder_upload(taskId, pcapPaths)
            else:
                # 单文件上传模式：只删除文件
                cleanup_success = self._cleanup_file_upload(pcapPaths)
            
            # 清理日志文件
            global syslogServer
            syslogServer.stop()
            self.clearSyslog()


            #取消设备日志外发的配置

            if self.pcIp and self.dut:
                        self.logger.info(f"找到与DUT设备 {self.dut.ip} 互通的本机IP: {self.pcIp}")
                        cmd='no logging syslog %s udp 514 type threat'%self.pcIp
                        self.dut.config(cmd)



            return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in afterTest: {str(e)}")
            return False

    def _is_folder_upload_mode(self, pcapPaths, taskId):
        """
        判断是否为文件夹上传模式
        通过检查文件路径结构来判断
        """
        if not pcapPaths or not taskId:
            return False

        try:
            task_dir = os.path.join(PATH, "uploads", "pcaps", str(taskId))

            # 检查是否有文件在子目录中
            has_subdirectory = False
            for path in pcapPaths:
                if task_dir in path:
                    relative_path = os.path.relpath(path, task_dir)
                    # 如果相对路径包含目录分隔符，说明有子目录结构
                    if '/' in relative_path or '\\' in relative_path:
                        has_subdirectory = True
                        break

            # 如果有子目录结构，或者任务目录存在且包含多个文件，认为是文件夹上传
            if has_subdirectory:
                self.logger.info(f"[TaskID: {taskId}] Detected folder upload mode (subdirectory structure found)")
                return True
            elif os.path.exists(task_dir) and len(pcapPaths) > 1:
                # 多个文件且在同一任务目录下，也可能是文件夹上传
                self.logger.info(f"[TaskID: {taskId}] Detected folder upload mode (multiple files in task directory)")
                return True

            self.logger.info(f"[TaskID: {taskId}] Detected single file upload mode")
            return False

        except Exception as e:
            self.logger.error(f"Error determining upload mode: {str(e)}")
            return False

    def _cleanup_folder_upload(self, taskId, pcapPaths=None):
        """
        清理文件夹上传的文件和目录结构

        Args:
            taskId: 任务ID
            pcapPaths: pcap文件路径列表（可选，用于兼容性）
        """
        try:
            task_dir = os.path.join(PATH, "uploads", "pcaps", str(taskId))

            if not os.path.exists(task_dir):
                self.logger.warning(f"[TaskID: {taskId}] Task directory not found: {task_dir}")
                return True

            # 统计目录信息
            total_files = 0
            total_dirs = 0

            for root, dirs, files in os.walk(task_dir):
                total_files += len(files)
                total_dirs += len(dirs)

            self.logger.info(f"[TaskID: {taskId}] Cleaning up folder upload: {total_files} files in {total_dirs} directories")

            # 删除整个任务目录
            import shutil
            shutil.rmtree(task_dir)
            self.logger.info(f"[TaskID: {taskId}] Successfully deleted entire task directory: {task_dir}")

            # 验证删除是否成功
            if os.path.exists(task_dir):
                self.logger.error(f"[TaskID: {taskId}] Task directory still exists after deletion attempt")
                return False

            self.logger.info(f"[TaskID: {taskId}] Folder upload cleanup completed: {total_files} files and {total_dirs} directories removed")

            return True

        except Exception as e:
            self.logger.error(f"[TaskID: {taskId}] Failed to cleanup folder upload: {str(e)}")
            self.logger.error(f"[TaskID: {taskId}] Error details: {traceback.format_exc()}")
            return False

    def _cleanup_file_upload(self, pcapPaths):
        """
        清理单文件上传的文件
        """
        cleanup_success = True

        if pcapPaths:
            for packetPath in pcapPaths:
                try:
                    if os.path.exists(packetPath):
                        os.remove(packetPath)
                        self.logger.info(f"Deleted temporary packet file: {packetPath}")
                    else:
                        self.logger.warning(f"Packet file not found for cleanup: {packetPath}")
                except Exception as e:
                    self.logger.error(f"Failed to delete packet file {packetPath}: {str(e)}")
                    cleanup_success = False

        return cleanup_success

    def report(self, **kwargs):
        try:
            # 参数验证
            if "results" not in kwargs or "resultDetail" not in kwargs:
                raise ValueError("Missing required parameters: results and resultDetail")

            results = kwargs["results"]
            resultSummary = kwargs["resultSummary"]
            resultDetail = kwargs["resultDetail"]
            #self.logger.info(f'result is:{results} ')
            #self.logger.info(f'resultSummary is:{resultSummary} ')
            #self.logger.info(f'resultDetail is:{resultsDetail} ')

            if not results:
                self.logger.warning("No results to report")
                return ""

            # 设置报告文件路径
            filePath = os.path.join(PATH, "reports", f"{self.taskId}.csv")

            try:
                os.makedirs(os.path.dirname(filePath), exist_ok=True)
                self.logger.info(f"Report directory created/verified: {os.path.dirname(filePath)}")
            except Exception as e:
                self.logger.error(f"Failed to create report directory: {str(e)}")
                raise

            # 写入CSV报告
            try:
                with open(filePath, mode='w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['Key', 'Result', 'count','Threat Name','Threat log']
                    # 设置CSV写入器参数，处理特殊字符
                    writer = csv.DictWriter(
                        csvfile,
                        fieldnames=fieldnames,
                        quoting=csv.QUOTE_ALL,  # 对所有字段加引号
                        escapechar='\\',        # 设置转义字符
                        doublequote=True        # 使用双引号转义
                    )

                    writer.writeheader()

                    for key in results.keys():
                        try:
                            count=0
                            # 清理数据，确保没有问题字符
                            clean_key = self._cleanCsvData(str(key))
                            clean_result = self._cleanCsvData(str(results.get(key, '')))

                            if resultSummary and resultSummary[key] != []:
                                count=len(resultSummary[key])
                                clean_name = ''
                                clean_detail = ''
                                for name in resultSummary[key]:
                                    clean_name = clean_name+self._cleanCsvData(str(name))+'\n'
                                for log in resultDetail[key]:
                                    clean_detail=clean_detail+self._cleanCsvData(str(log))+'\n'    
                                writer.writerow({
                                        'Key': clean_key,
                                        'Result': clean_result,
                                        'count': count,
                                        'Threat Name': clean_name,
                                        'Threat log': clean_detail,
                                    })    
                            else:
                                writer.writerow({
                                    'Key': clean_key,
                                    'Result': clean_result,
                                    'count': count,
                                    'Threat Name': '',
                                    'Threat log': '',
                                })
                        except Exception as e:
                            self.logger.error(f"Error writing row for key {key}: {str(e)}")
                            

                self.logger.info(f"Test results written to CSV file: {filePath}")

                # 统计结果
                total_tests = len(results)
                successful_detections = sum(1 for result in results.values() if result)
                self.logger.info(f"Test summary: {successful_detections}/{total_tests} detections successful")

                return filePath

            except Exception as e:
                self.logger.error(f"Failed to write CSV report: {str(e)}")
                raise

        except Exception as e:
            self.logger.error(f"Error in report generation: {str(e)}")
            return ""


class LogServer(object):
    """
    UDP日志服务器类
    用于接收和解析标准的网络日志（如Syslog），并按日期写入日志文件
    监听UDP协议的514端口
    """

    def __init__(self, **kwargs):
        """
        初始化日志服务器

        Args:
            host (str): 监听的主机地址，默认为 '0.0.0.0'
            port (int): 监听的端口号，默认为 514
            logDir (str): 日志文件存储目录，默认为 './logs/syslog'
            max_buffer_size (int): UDP接收缓冲区大小，默认为 65536
            task_id (str): 任务ID，用于日志标识
        """
        try:
            self.host = kwargs.get('host', '0.0.0.0')
            self.port = kwargs.get('port', 514)
            self.logDir = kwargs.get('logDir', os.path.join(PATH, 'logs', 'syslog'))
            self.max_buffer_size = kwargs.get('max_buffer_size', 65536)
            self.task_id = kwargs.get('task_id', None)
            self.is_running = False
            self.socket = None
            self.server_thread = None

            # 如果提供了task_id，设置到线程本地存储
            if self.task_id:
                set_task_id(self.task_id)

            global logger
            self.logger = init_logger(logger)

            

            # 确保日志目录存在
            os.makedirs(self.logDir, exist_ok=True)

            self.logger.info(f"LogServer initialized - Host: {self.host}, Port: {self.port}, LogDir: {self.logDir}")

        except Exception as e:
            self.logger.error(f"Error initializing LogServer: {str(e)}")
            raise

    def _parse_syslog_message(self, data, client_address):
        """
        解析Syslog消息

        Args:
            data (bytes): 接收到的原始数据
            client_address (tuple): 客户端地址信息 (ip, port)

        Returns:
            dict: 解析后的日志信息
        """
        try:
            # 尝试解码数据
            try:
                message = data.decode('utf-8').strip()
            except UnicodeDecodeError:
                try:
                    message = data.decode('latin-1').strip()
                except UnicodeDecodeError:
                    message = data.decode('utf-8', errors='ignore').strip()

            if not message:
                return None

            # 解析Syslog格式 <priority>timestamp hostname tag: message
            parsed_log = {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'client_ip': client_address[0],
                'client_port': client_address[1],
                'raw_message': message,
                'priority': None,
                'facility': None,
                'severity': None,
                'hostname': 'unknown',
                'tag': 'unknown',
                'content': message
            }

            # 尝试解析RFC3164格式的Syslog消息
            if message.startswith('<') and '>' in message:
                try:
                    # 提取优先级
                    priority_end = message.find('>')
                    if priority_end > 0:
                        priority_str = message[1:priority_end]
                        if priority_str.isdigit():
                            priority = int(priority_str)
                            parsed_log['priority'] = priority
                            parsed_log['facility'] = priority >> 3  # 设施 = 优先级 / 8
                            parsed_log['severity'] = priority & 7   # 严重性 = 优先级 % 8

                            # 移除优先级部分
                            message = message[priority_end + 1:].strip()

                    # 尝试解析时间戳、主机名和标签
                    parts = message.split(' ', 3)
                    if len(parts) >= 3:
                        # 简单的时间戳检测（可能包含月份名称）
                        if any(month in parts[0] for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                                               'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                            parsed_log['timestamp'] = f"{parts[0]} {parts[1]}"
                            parsed_log['hostname'] = parts[2]
                            if len(parts) > 3:
                                content_parts = parts[3].split(':', 1)
                                if len(content_parts) == 2:
                                    parsed_log['tag'] = content_parts[0].strip()
                                    parsed_log['content'] = content_parts[1].strip()
                                else:
                                    parsed_log['content'] = parts[3]
                        else:
                            parsed_log['hostname'] = parts[0]
                            if len(parts) > 1:
                                content_parts = parts[1].split(':', 1)
                                if len(content_parts) == 2:
                                    parsed_log['tag'] = content_parts[0].strip()
                                    parsed_log['content'] = content_parts[1].strip()
                                else:
                                    parsed_log['content'] = ' '.join(parts[1:])

                except Exception as parse_error:
                    self.logger.debug(f"Error parsing syslog format: {str(parse_error)}")
                    # 如果解析失败，保持原始消息
                    pass

            return parsed_log

        except Exception as e:
            self.logger.error(f"Error parsing syslog message: {str(e)}")
            return {
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'client_ip': client_address[0],
                'client_port': client_address[1],
                'raw_message': str(data),
                'content': 'Parse error: ' + str(e)
            }

    def _write_log_to_file(self, parsed_log):
        """
        将解析后的日志写入文件

        Args:
            parsed_log (dict): 解析后的日志信息
        """
        try:
            # 生成日志文件名（按日期）
            current_date = datetime.datetime.now().strftime('%Y-%m-%d')
            log_filename = f"syslog_{current_date}.log"
            log_filepath = os.path.join(self.logDir, log_filename)

            # 格式化日志条目
            log_entry = (
                f"[{parsed_log['timestamp']}] "
                f"FROM:{parsed_log['client_ip']}:{parsed_log['client_port']} "
                f"HOST:{parsed_log.get('hostname', 'unknown')} "
                f"TAG:{parsed_log.get('tag', 'unknown')} "
                f"PRIORITY:{parsed_log.get('priority', 'N/A')} "
                f"FACILITY:{parsed_log.get('facility', 'N/A')} "
                f"SEVERITY:{parsed_log.get('severity', 'N/A')} "
                f"CONTENT:{parsed_log.get('content', '')}\n"
            )

            # 写入日志文件
            with open(log_filepath, 'a', encoding='utf-8') as f:
                f.write(log_entry)

            self.logger.debug(f"Log written to {log_filepath}: {parsed_log.get('content', '')[:100]}...")

        except Exception as e:
            self.logger.error(f"Error writing log to file: {str(e)}")

    def _server_loop(self):
        """
        服务器主循环
        """
        try:
            self.logger.info(f"LogServer started listening on {self.host}:{self.port}")

            while self.is_running:
                try:
                    # 设置超时，以便能够检查is_running状态
                    self.socket.settimeout(1.0)

                    # 接收数据
                    data, client_address = self.socket.recvfrom(self.max_buffer_size)

                    if not self.is_running:
                        break

                    self.logger.debug(f"Received {len(data)} bytes from {client_address}")

                    # 解析日志消息
                    parsed_log = self._parse_syslog_message(data, client_address)

                    if parsed_log:
                        # 写入日志文件
                        self._write_log_to_file(parsed_log)

                        # 记录接收到的日志
                        self.logger.info(f"Received log from {client_address[0]}: {parsed_log.get('content', '')[:100]}...")

                except socket.timeout:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    if self.is_running:
                        self.logger.error(f"Error in server loop: {str(e)}")
                        sleep(1)  # 避免快速循环

        except Exception as e:
            self.logger.error(f"Fatal error in server loop: {str(e)}")
        finally:
            self.logger.info("LogServer stopped")

    def start(self):
        """
        启动日志服务器

        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                self.logger.warning("LogServer is already running")
                return True

            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

            # 设置套接字选项
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # 绑定地址和端口
            self.socket.bind((self.host, self.port))

            # 设置运行标志
            self.is_running = True

            # 启动服务器线程
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()

            self.logger.info(f"LogServer started successfully on {self.host}:{self.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start LogServer: {str(e)}")
            self.is_running = False
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
            return False

    def stop(self):
        """
        停止日志服务器

        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_running:
                self.logger.warning("LogServer is not running")
                return True

            self.logger.info("Stopping LogServer...")

            # 设置停止标志
            self.is_running = False

            # 关闭套接字
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None

            # 等待服务器线程结束
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)
                if self.server_thread.is_alive():
                    self.logger.warning("Server thread did not stop gracefully")

            self.logger.info("LogServer stopped successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error stopping LogServer: {str(e)}")
            return False

    def is_server_running(self):
        """
        检查服务器是否正在运行

        Returns:
            bool: 服务器运行状态
        """
        return self.is_running and self.server_thread and self.server_thread.is_alive()

    def get_log_files(self):
        """
        获取所有日志文件列表

        Returns:
            list: 日志文件路径列表
        """
        try:
            if not os.path.exists(self.logDir):
                return []

            log_files = []
            for filename in os.listdir(self.logDir):
                if filename.startswith('syslog_') and filename.endswith('.log'):
                    log_files.append(os.path.join(self.logDir, filename))

            # 按修改时间排序
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            return log_files

        except Exception as e:
            self.logger.error(f"Error getting log files: {str(e)}")
            return []

    def get_server_stats(self):
        """
        获取服务器统计信息

        Returns:
            dict: 服务器统计信息
        """
        try:
            stats = {
                'is_running': self.is_server_running(),
                'host': self.host,
                'port': self.port,
                'logDir': self.logDir,
                'log_files_count': len(self.get_log_files()),
                'uptime': 'N/A'
            }

            return stats

        except Exception as e:
            self.logger.error(f"Error getting server stats: {str(e)}")
            return {}
# 使用统一的logs目录结构
logDir=os.path.join(PATH, 'logs', 'syslog')
syslogServer = LogServer(logDir=logDir)
                         
