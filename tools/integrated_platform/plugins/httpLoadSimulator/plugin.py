# -*- coding: utf-8 -*-
"""
HTTP负载模拟访问插件主类
"""

import os
import json
import uuid
import threading
import zipfile

from datetime import datetime
from typing import List, Dict, Any, Optional
from flask import render_template, request, jsonify, send_file, current_app

from core.base_plugin import BasePlugin
from .http_load_simulator import HttpLoadSimulator


class Plugin(BasePlugin):
    """HTTP负载模拟访问插件"""

    def __init__(self, **kwargs):
        # 先调用父类初始化
        super().__init__(**kwargs)
        
        # 设置插件信息
        self.name = "httpLoadSimulator"
        self.displayName = "HTTP负载模拟访问"
        self.description = "支持模拟发送自定义HTTP负载请求，生成网络报文抓包文件"
        self.version = "1.0.0"
        self.author = "JBLIAO"
        
        # 初始化插件目录
        self.pluginDir = os.path.dirname(os.path.abspath(__file__))
        self.uploadsDir = os.path.join(self.pluginDir, 'uploads')
        self.reportsDir = os.path.join(self.pluginDir, 'reports')
        self.logsDir = os.path.join(self.pluginDir, 'logs')
        
        # 确保目录存在
        for directory in [self.uploadsDir, self.reportsDir, self.logsDir]:
            if not os.path.exists(directory):
                os.makedirs(directory)

        #初始化插件日志
        self.logger = self._setup_plugin_logger()
        
        # 任务管理
        self.tasks = {}
        self.taskLock = threading.Lock()
        self.tasksFile = os.path.join(self.pluginDir, 'tasks.json')
        self._loadTasks()
    def _setup_plugin_logger(self):
        """
        设置插件统一的日志配置

        Returns:
            logging.Logger: 配置好的日志记录器

        功能：
        - 创建插件专用的日志记录器
        - 统一日志格式和存储位置
        - 支持TaskID追踪功能
        """
        import logging.handlers
        from datetime import datetime

        # 创建插件专用logger
        logger_name = f"plugin.{self.name}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # 清除现有处理器，避免重复
        if logger.handlers:
            logger.handlers.clear()


        # 禁用向根日志记录器传播，避免重复记录
        logger.propagate = False

        # 创建统一的日志格式化器
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        formatter = logging.Formatter(log_format, datefmt='%Y-%m-%d %H:%M:%S')

        # 创建文件处理器 - 主日志文件
        main_log_file = os.path.join(self.logsDir, 'plugin.log')
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 创建错误日志处理器
        error_log_file = os.path.join(self.logsDir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)

        #logger.info(f"插件日志系统初始化完成 - 日志目录: {self.logsDir}")
        return logger


    def _loadTasks(self):
        """加载任务数据"""
        try:
            if os.path.exists(self.tasksFile):
                with open(self.tasksFile, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
        except Exception as e:
            self.logger.error(f"加载任务数据失败: {str(e)}")
            self.tasks = {}

    def _saveTasks(self):
        """保存任务数据"""
        try:
            with open(self.tasksFile, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存任务数据失败: {str(e)}")

    def getInfo(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'displayName': self.displayName,
            'description': self.description,
            'version': self.version,
            'author': self.author
        }

    def getRoutes(self) -> List[Dict[str, Any]]:
        """获取页面路由配置"""
        return [
            {
                "rule": "/",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "index"
            },
            {
                "rule": "/create-task",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "create_task_page"
            },
            {
                "rule": "/task-results",
                "view_func": self.taskResultsPage,
                "methods": ["GET"],
                "endpoint": "task_results_page"
            }
        ]

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """获取API路由配置"""
        return [
            # 任务管理API
            {
                "rule": "/tasks",
                "view_func": self.getTasks,
                "methods": ["GET"],
                "endpoint": "get_tasks"
            },
            {
                "rule": "/tasks",
                "view_func": self.createTask,
                "methods": ["POST"],
                "endpoint": "create_task"
            },
            {
                "rule": "/batch-tasks",
                "view_func": self.createBatchTask,
                "methods": ["POST"],
                "endpoint": "create_batch_task"
            },
            {
                "rule": "/tasks/<task_id>/status",
                "view_func": self.getTaskStatus,
                "methods": ["GET"],
                "endpoint": "get_task_status"
            },
            {
                "rule": "/tasks/<task_id>/download-report",
                "view_func": self.downloadTaskReport,
                "methods": ["GET"],
                "endpoint": "download_task_report"
            },
            {
                "rule": "/tasks/<task_id>/delete",
                "view_func": self.deleteTask,
                "methods": ["DELETE"],
                "endpoint": "delete_task"
            },
            {
                "rule": "/tasks/batch-delete",
                "view_func": self.batchDeleteTasks,
                "methods": ["POST"],
                "endpoint": "batch_delete_tasks"
            }
        ]

    def getNavItems(self) -> List[Dict[str, Any]]:
        """获取导航项配置"""
        return [
            {
                "title": "HTTP负载模拟访问",
                "url": "/plugins/httpLoadSimulator/",
                "icon": "fas fa-server",
                "order": 40
            }
        ]

    def onInitialize(self):
        """插件初始化回调"""
        self.logger.info("HTTP负载模拟访问插件初始化完成")
        
        # 初始化HTTP负载模拟器
        self.httpLoadSimulator = HttpLoadSimulator(
            plugin_dir=self.pluginDir,
            logger=self.logger
        )

    def createTaskPage(self):
        """创建任务页面"""
        try:
            return render_template('httpLoadSimulator/create_task.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载创建任务页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def taskResultsPage(self):
        """任务结果页面"""
        try:
            return render_template('httpLoadSimulator/task_results.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载任务结果页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def getTasks(self):
        """获取任务列表"""
        try:
            with self.taskLock:
                taskList = []
                for taskId, task in self.tasks.items():
                    taskList.append({
                        'id': taskId,
                        'name': task.get('name', ''),
                        'status': task.get('status', 'unknown'),
                        'createTime': task.get('createTime', ''),
                        'finishTime': task.get('finishTime', ''),
                        'result': task.get('result', ''),
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''), task)
                    })
                
                # 按创建时间倒序排序（最新的任务在前面）
                taskList.sort(key=lambda x: x.get('createTime', ''), reverse=True)
                
                return jsonify({
                    'success': True,
                    'data': taskList
                })
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务列表失败: {str(e)}"
            }), 500

    def _hasReportFiles(self, reportDir: str, task: Dict[str, Any] = None) -> bool:
        """检查是否有有效的pcap报文文件"""
        if not reportDir or not os.path.exists(reportDir):
            return False

        # 检查报告目录中是否有有效的pcap文件（文件大小大于0）
        for file in os.listdir(reportDir):
            if file.endswith('.pcap'):
                filePath = os.path.join(reportDir, file)
                if os.path.exists(filePath) and os.path.getsize(filePath) > 0:
                    return True
        return False

    def createTask(self):
        """创建HTTP负载模拟任务"""
        try:
            # 获取JSON数据
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据格式错误'
                }), 400

            taskName = data.get('taskName', '').strip()
            httpPayload = data.get('httpPayload', '').strip()
            targetIp = data.get('targetIp', '').strip()
            targetPort = data.get('targetPort', 80)

            # 验证参数
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400

            if not httpPayload:
                return jsonify({
                    'success': False,
                    'message': 'HTTP负载不能为空'
                }), 400

            if not targetIp:
                return jsonify({
                    'success': False,
                    'message': '目标IP地址不能为空'
                }), 400

            # 生成任务ID
            taskId = str(uuid.uuid4())

            # 创建任务记录
            task = {
                'id': taskId,
                'name': taskName,
                'httpPayload': httpPayload,
                'targetIp': targetIp,
                'targetPort': int(targetPort),
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': os.path.join(self.reportsDir, taskId)
            }

            with self.taskLock:
                self.tasks[taskId] = task
                self._saveTasks()

            # 启动后台任务
            thread = threading.Thread(
                target=self._executeTask,
                args=(taskId,),
                daemon=True
            )
            thread.start()

            return jsonify({
                'success': True,
                'message': '任务创建成功',
                'data': {'taskId': taskId}
            })

        except Exception as e:
            self.logger.error(f"创建任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建任务失败: {str(e)}"
            }), 500

    def createBatchTask(self):
        """创建批量HTTP负载模拟任务"""
        try:
            self.logger.info("开始处理批量任务创建请求")

            # 获取表单数据
            taskName = request.form.get('taskName', '').strip()
            targetIp = request.form.get('targetIp', '').strip()
            targetPort = request.form.get('targetPort', '').strip()
            csvFile = request.files.get('csvFile')

            self.logger.info(f"接收到参数: taskName={taskName}, targetIp={targetIp}, targetPort={targetPort}, csvFile={csvFile.filename if csvFile else None}")

            # 验证参数
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400

            if not targetIp:
                return jsonify({
                    'success': False,
                    'message': '目标IP地址不能为空'
                }), 400

            if not targetPort:
                return jsonify({
                    'success': False,
                    'message': '目标端口不能为空'
                }), 400

            try:
                targetPort = int(targetPort)
                if targetPort < 1 or targetPort > 65535:
                    raise ValueError()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': '目标端口必须是1-65535之间的数字'
                }), 400

            if not csvFile:
                return jsonify({
                    'success': False,
                    'message': '请上传CSV文件'
                }), 400

            # 验证文件类型
            if not csvFile.filename.lower().endswith('.csv'):
                return jsonify({
                    'success': False,
                    'message': '请上传CSV格式的文件'
                }), 400

            # 生成批量任务ID
            batchTaskId = str(uuid.uuid4())

            # 创建批量任务目录
            batchTaskDir = os.path.join(self.reportsDir, batchTaskId)
            os.makedirs(batchTaskDir, exist_ok=True)

            # 处理并保存CSV文件
            csvFilePath = os.path.join(batchTaskDir, f'{taskName}.csv')
            csvData = self._processAndSaveCsvFile(csvFile, csvFilePath)
            if not csvData:
                return jsonify({
                    'success': False,
                    'message': 'CSV文件格式错误或为空'
                }), 400

            # 创建批量任务记录
            batchTask = {
                'id': batchTaskId,
                'name': taskName,
                'type': 'batch',
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': batchTaskDir,
                'csvFilePath': csvFilePath,
                'targetIp': targetIp,
                'targetPort': targetPort,
                'totalItems': len(csvData),
                'completedItems': 0,
                'failedItems': 0
            }

            with self.taskLock:
                self.tasks[batchTaskId] = batchTask
                self._saveTasks()

            # 启动批量任务执行
            thread = threading.Thread(
                target=self._executeBatchTask,
                args=(batchTaskId,),
                daemon=True
            )
            thread.start()

            return jsonify({
                'success': True,
                'message': f'批量任务创建成功，共{len(csvData)}个测试项目',
                'data': {'taskId': batchTaskId}
            })

        except Exception as e:
            self.logger.error(f"创建批量任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建批量任务失败: {str(e)}"
            }), 500

    def getTaskStatus(self, task_id):
        """获取任务状态"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                return jsonify({
                    'success': True,
                    'data': {
                        'id': task['id'],
                        'name': task['name'],
                        'status': task['status'],
                        'result': task['result'],
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''), task)
                    }
                })
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务状态失败: {str(e)}"
            }), 500

    def _executeTask(self, task_id):
        """执行HTTP负载模拟任务"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return

                task['status'] = 'running'
                self._saveTasks()

            # 确保报告目录存在
            os.makedirs(task['reportDir'], exist_ok=True)

            # 执行HTTP负载模拟
            result = self.httpLoadSimulator.simulateHttpLoad(
                http_payload=task['httpPayload'],
                target_ip=task['targetIp'],
                target_port=task['targetPort'],
                output_dir=task['reportDir']
            )

            with self.taskLock:
                task['status'] = 'completed' if result['success'] else 'failed'
                task['result'] = result['message']
                task['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()

        except Exception as e:
            self.logger.error(f"执行任务失败: {str(e)}")
            with self.taskLock:
                if task_id in self.tasks:
                    self.tasks[task_id]['status'] = 'failed'
                    self.tasks[task_id]['result'] = f'任务执行失败: {str(e)}'
                    self.tasks[task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def _executeBatchTask(self, batch_task_id):
        """执行批量HTTP负载模拟任务（串行执行）"""
        try:
            with self.taskLock:
                batchTask = self.tasks.get(batch_task_id)
                if not batchTask:
                    return

                batchTask['status'] = 'running'
                self._saveTasks()

            self.logger.info(f"开始执行批量任务: {batch_task_id}，共{batchTask['totalItems']}个测试项目")

            # 确保批量任务报告目录存在
            os.makedirs(batchTask['reportDir'], exist_ok=True)

            completedCount = 0
            failedCount = 0
            results=[]

            # 从保存的CSV文件读取数据
            csvFilePath = batchTask.get('csvFilePath', '')
            if not csvFilePath or not os.path.exists(csvFilePath):
                self.logger.error(f"CSV文件不存在: {csvFilePath}")
                with self.taskLock:
                    batchTask['status'] = 'failed'
                    batchTask['result'] = 'CSV文件不存在'
                    batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()
                return

            csvData = self._parseCsvFile(csvFilePath)
            if not csvData:
                self.logger.error(f"解析CSV文件失败: {csvFilePath}")
                with self.taskLock:
                    batchTask['status'] = 'failed'
                    batchTask['result'] = '解析CSV文件失败'
                    batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()
                return

            # 获取目标IP和端口
            targetIp = batchTask.get('targetIp', '127.0.0.1')
            targetPort = batchTask.get('targetPort', 8080)

            # 串行执行每个测试项目
            for index, item in enumerate(csvData):
                try:
                    testName = item.get('testName', f'测试项目{index + 1}')
                    httpPayload = item.get('httpPayload', '')

                    self.logger.info(f"执行测试项目 {index + 1}/{len(csvData)}: {testName}")

                    # 清理测试项目名称，用作文件名
                    cleanTestName = "".join(c for c in testName if c.isalnum() or c in (' ', '-', '_')).strip()
                    cleanTestName = cleanTestName.replace(' ', '_')
                    if not cleanTestName:
                        cleanTestName = f'test_{index + 1}'

                    # 执行HTTP负载模拟，pcap文件直接保存到批量任务目录
                    result = self.httpLoadSimulator.simulateHttpLoad(
                        http_payload=httpPayload,
                        target_ip=targetIp,
                        target_port=targetPort,
                        output_dir=batchTask['reportDir'],
                        pcap_filename_prefix=cleanTestName  # 使用测试项目名称作为pcap文件前缀
                    )

                    # 记录测试结果
                    status = "成功" if result['success'] else "失败"
                    results.append({
                        '测试项目': testName,
                        '状态': status,
                        '结果信息': result['message'],
                        'HTTP负载': httpPayload,  # 保持原始格式，不转义换行符
                        '执行时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

                    if result['success']:
                        completedCount += 1
                        self.logger.info(f"测试项目 '{testName}' 执行成功")
                    else:
                        failedCount += 1
                        self.logger.error(f"测试项目 '{testName}' 执行失败: {result['message']}")

                    # 更新批量任务进度
                    with self.taskLock:
                        batchTask['completedItems'] = completedCount
                        batchTask['failedItems'] = failedCount
                        self._saveTasks()

                except Exception as e:
                    failedCount += 1
                    error_message = str(e)
                    self.logger.error(f"执行测试项目 '{testName}' 失败: {error_message}")

                    # 记录失败的测试结果
                    results.append({
                        '测试项目': testName,
                        '状态': "失败",
                        '结果信息': error_message,
                        'HTTP负载': httpPayload if 'httpPayload' in locals() else '',  # 保持原始格式
                        '执行时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

                    # 更新批量任务进度
                    with self.taskLock:
                        batchTask['completedItems'] = completedCount
                        batchTask['failedItems'] = failedCount
                        self._saveTasks()

            # 写入结果CSV文件
            newFile = os.path.join(batchTask['reportDir'], f'{batchTask["id"]}_results.csv')
            self._writeResultsToCsv(results, newFile)

            # 完成批量任务
            with self.taskLock:
                batchTask['status'] = 'completed'
                batchTask['result'] = f'批量任务完成，成功: {completedCount}个，失败: {failedCount}个'
                batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()

            self.logger.info(f"批量任务 {batch_task_id} 执行完成，成功: {completedCount}个，失败: {failedCount}个")
            self.logger.info(f"结果已保存到: {newFile}")

        except Exception as e:
            self.logger.error(f"执行批量任务失败: {str(e)}")
            with self.taskLock:
                if batch_task_id in self.tasks:
                    self.tasks[batch_task_id]['status'] = 'failed'
                    self.tasks[batch_task_id]['result'] = f'批量任务执行失败: {str(e)}'
                    self.tasks[batch_task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def downloadTaskReport(self, task_id):
        """下载任务报文压缩包"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                reportDir = task.get('reportDir', '')
                if not self._hasReportFiles(reportDir, task):
                    return jsonify({
                        'success': False,
                        'message': '任务报文文件不存在'
                    }), 500

                zipPath = self._createTaskReportZip(task_id, task, reportDir)
                if not zipPath:
                    return jsonify({
                        'success': False,
                        'message': '创建报文压缩包失败'
                    }), 500

                return send_file(
                    zipPath,
                    as_attachment=True,
                    download_name=f"http_load_report_{task_id}.zip"
                )
        except Exception as e:
            self.logger.error(f"下载任务报文压缩包失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"下载失败: {str(e)}"
            }), 500

    def deleteTask(self, task_id):
        """删除任务"""
        try:
            with self.taskLock:
                if task_id not in self.tasks:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                task = self.tasks[task_id]
                reportDir = task.get('reportDir', '')

                # 删除报告目录
                if reportDir and os.path.exists(reportDir):
                    import shutil
                    shutil.rmtree(reportDir)
                
                #删除压缩文件报告
                zipPath = os.path.join(self.reportsDir, f"http_load_report_{task_id}.zip")
                if os.path.exists(zipPath):
                    os.remove(zipPath)

                # 删除任务记录
                del self.tasks[task_id]
                self._saveTasks()

            return jsonify({
                'success': True,
                'message': '任务删除成功'
            })
        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"删除任务失败: {str(e)}"
            }), 500

    def batchDeleteTasks(self):
        """批量删除任务"""
        try:
            data = request.get_json()
            if not data or 'taskIds' not in data:
                return jsonify({
                    'success': False,
                    'message': '请求参数错误'
                }), 400

            taskIds = data['taskIds']
            if not isinstance(taskIds, list):
                return jsonify({
                    'success': False,
                    'message': '任务ID列表格式错误'
                }), 400

            deletedCount = 0
            with self.taskLock:
                for taskId in taskIds:
                    if taskId in self.tasks:
                        task = self.tasks[taskId]
                        reportDir = task.get('reportDir', '')

                        # 删除报告目录
                        if reportDir and os.path.exists(reportDir):
                            import shutil
                            shutil.rmtree(reportDir)

                        # 删除任务记录
                        del self.tasks[taskId]
                        deletedCount += 1

                self._saveTasks()

            return jsonify({
                'success': True,
                'message': f'成功删除 {deletedCount} 个任务',
                'data': {'deletedCount': deletedCount}
            })
        except Exception as e:
            self.logger.error(f"批量删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"批量删除任务失败: {str(e)}"
            }), 500

    def _createTaskReportZip(self, task_id: str, task: Dict[str, Any], reportDir: str) -> str:
        """创建任务报文压缩包，只包含pcap报文文件"""
        try:
            # 创建临时压缩文件
            zipPath = os.path.join(self.reportsDir, f"http_load_report_{task_id}.zip")

            # 如果压缩文件已存在，先删除
            if os.path.exists(zipPath):
                try:
                    os.remove(zipPath)
                    self.logger.info(f"删除已存在的压缩文件: {zipPath}")
                except Exception as e:
                    self.logger.warning(f"删除已存在压缩文件失败: {str(e)}")

            with zipfile.ZipFile(zipPath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                pcapCount = 0

                # 直接添加报告目录中的所有pcap文件
                if os.path.exists(reportDir):
                    for filename in os.listdir(reportDir):
                        if filename.endswith('.pcap'):
                            filePath = os.path.join(reportDir, filename)
                            if os.path.exists(filePath) and os.path.getsize(filePath) > 0:
                                # 直接使用原文件名，已经包含了测试项目名称前缀
                                zipf.write(filePath, filename)
                                pcapCount += 1
                                self.logger.info(f"添加报文文件到压缩包: {filename}")
                        if filename.endswith('.csv'):
                            filePath = os.path.join(reportDir, filename)
                            if os.path.exists(filePath) and os.path.getsize(filePath) > 0:
                                # 直接使用原文件名，已经包含了测试项目名称前缀
                                zipf.write(filePath, filename)
                                self.logger.info(f"添加测试结果文件到压缩包: {filename}")

                if pcapCount == 0:
                    self.logger.warning(f"任务 {task_id} 没有找到有效的pcap报文文件")
                    return None

            self.logger.info(f"成功创建任务报文压缩包: {zipPath}，包含 {pcapCount} 个pcap文件")
            return zipPath

        except Exception as e:
            self.logger.error(f"创建任务报文压缩包失败: {str(e)}")
            return None

    def _processAndSaveCsvFile(self, csvFile, csvFilePath: str) -> List[Dict[str, str]]:
        """处理上传的CSV文件并以标准格式保存，支持混合编码内容"""
        try:
            import csv
            import chardet
            import io
            import re

            # 读取上传文件的原始数据
            csvFile.seek(0)  # 确保从文件开头读取
            rawData = csvFile.read()

            self.logger.info(f"开始处理CSV文件，原始数据大小: {len(rawData)} 字节")

            # 第一步：尝试按行分割原始字节数据
            csvData = self._parseRawCsvData(rawData)

            if not csvData:
                self.logger.error("无法解析CSV文件数据")
                return []

            # 第二步：以标准格式重新保存CSV文件
            self._saveStandardCsvFile(csvData, csvFilePath)

            return csvData

        except Exception as e:
            self.logger.error(f"处理上传CSV文件失败: {str(e)}")
            return []

    def _parseRawCsvData(self, rawData: bytes) -> List[Dict[str, str]]:
        """解析原始CSV字节数据，支持混合编码"""
        try:
            import csv
            import chardet
            import io

            csvData = []

            # 第一步：尝试将原始数据按行分割
            lines = self._splitCsvLines(rawData)
            if not lines:
                return []

            self.logger.info(f"检测到 {len(lines)} 行数据")

            # 第二步：逐行处理，支持混合编码
            for lineIndex, lineBytes in enumerate(lines):
                if lineIndex == 0:
                    # 跳过标题行，但验证格式
                    headerText = self._decodeLineWithMultipleEncodings(lineBytes)
                    if not headerText or ',' not in headerText:
                        self.logger.error("CSV文件格式错误：标题行格式不正确")
                        return []
                    continue

                # 解析数据行
                lineText = self._decodeLineWithMultipleEncodings(lineBytes)
                if not lineText:
                    self.logger.warning(f"第 {lineIndex + 1} 行解码失败，跳过")
                    continue

                # 解析CSV行数据
                rowData = self._parseCsvRow(lineText, lineIndex + 1)
                if rowData:
                    csvData.append(rowData)

            self.logger.info(f"成功解析 {len(csvData)} 个有效测试项目")
            return csvData

        except Exception as e:
            self.logger.error(f"解析原始CSV数据失败: {str(e)}")
            return []

    def _splitCsvLines(self, rawData: bytes) -> List[bytes]:
        """智能分割CSV行，考虑HTTP负载中的特殊字符"""
        try:
            # 首先尝试将字节数据解码为字符串进行智能分析
            textData = self._decodeRawDataSafely(rawData)
            if not textData:
                return []

            # 使用智能行分割算法
            lines = self._smartLineSplit(textData)

            # 将分割后的行重新编码为字节
            lineBytes = []
            for line in lines:
                if line.strip():
                    # 尝试保持原始编码
                    lineBytes.append(line.encode('utf-8'))

            self.logger.info(f"智能分割得到 {len(lineBytes)} 行")
            return lineBytes

        except Exception as e:
            self.logger.error(f"智能分割CSV行失败: {str(e)}")
            # 回退到简单分割
            return self._fallbackLineSplit(rawData)

    def _decodeRawDataSafely(self, rawData: bytes) -> str:
        """安全解码原始数据，尝试多种编码"""
        import chardet

        # 检测编码
        encodingResult = chardet.detect(rawData)
        detectedEncoding = encodingResult['encoding'] if encodingResult['encoding'] else 'utf-8'

        # 尝试多种编码
        encodingsToTry = [detectedEncoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']

        for encoding in encodingsToTry:
            try:
                return rawData.decode(encoding)
            except (UnicodeDecodeError, UnicodeError):
                continue

        # 最后的备选方案
        try:
            return rawData.decode('utf-8', errors='replace')
        except:
            return rawData.decode('latin1', errors='replace')

    def _smartLineSplit(self, textData: str) -> List[str]:
        """智能行分割：只在非引号内的换行符处分割"""
        lines = []
        currentLine = ""
        inQuotes = False
        escapeNext = False
        i = 0

        while i < len(textData):
            char = textData[i]

            if escapeNext:
                # 如果前一个字符是转义符，当前字符不做特殊处理
                currentLine += char
                escapeNext = False
            elif char == '\\':
                # 转义符
                currentLine += char
                escapeNext = True
            elif char == '"':
                # 双引号：切换引号状态
                currentLine += char
                inQuotes = not inQuotes
            elif char == '\n' and not inQuotes:
                # 换行符且不在引号内：这是真正的行分割
                if currentLine.strip():
                    lines.append(currentLine)
                currentLine = ""
            elif char == '\r':
                # 回车符：检查是否是\r\n
                if i + 1 < len(textData) and textData[i + 1] == '\n':
                    # \r\n组合
                    if not inQuotes:
                        if currentLine.strip():
                            lines.append(currentLine)
                        currentLine = ""
                        i += 1  # 跳过\n
                    else:
                        currentLine += char
                else:
                    # 单独的\r
                    if not inQuotes:
                        if currentLine.strip():
                            lines.append(currentLine)
                        currentLine = ""
                    else:
                        currentLine += char
            else:
                currentLine += char

            i += 1

        # 添加最后一行
        if currentLine.strip():
            lines.append(currentLine)

        self.logger.info(f"智能行分割完成，共 {len(lines)} 行")
        return lines

    def _fallbackLineSplit(self, rawData: bytes) -> List[bytes]:
        """备用的简单行分割方法"""
        try:
            line_separators = [b'\r\n', b'\n', b'\r']

            for separator in line_separators:
                if separator in rawData:
                    lines = rawData.split(separator)
                    lines = [line for line in lines if line.strip()]
                    if len(lines) >= 2:
                        self.logger.info(f"使用备用分割方法，分隔符 {separator}，得到 {len(lines)} 行")
                        return lines

            return [rawData] if rawData.strip() else []

        except Exception as e:
            self.logger.error(f"备用分割方法失败: {str(e)}")
            return []

    def _decodeLineWithMultipleEncodings(self, lineBytes: bytes) -> str:
        """使用多种编码尝试解码单行数据"""
        try:
            import chardet

            if not lineBytes:
                return ""

            # 首先尝试检测这一行的编码
            encodingResult = chardet.detect(lineBytes)
            detectedEncoding = encodingResult['encoding'] if encodingResult['encoding'] else 'utf-8'

            # 定义编码尝试顺序，优先使用检测到的编码
            encodingsToTry = [detectedEncoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1', 'cp1252']

            # 去重并保持顺序
            seen = set()
            uniqueEncodings = []
            for enc in encodingsToTry:
                if enc and enc not in seen:
                    seen.add(enc)
                    uniqueEncodings.append(enc)

            # 尝试每种编码
            for encoding in uniqueEncodings:
                try:
                    decodedText = lineBytes.decode(encoding)
                    # 验证解码结果是否合理（包含可打印字符）
                    if self._isValidDecodedText(decodedText):
                        self.logger.debug(f"成功使用编码 {encoding} 解码行数据")
                        return decodedText
                except (UnicodeDecodeError, UnicodeError):
                    continue
                except Exception as e:
                    self.logger.debug(f"编码 {encoding} 解码时出错: {str(e)}")
                    continue

            # 如果所有编码都失败，使用错误处理方式强制解码
            try:
                return lineBytes.decode('utf-8', errors='replace')
            except:
                return lineBytes.decode('latin1', errors='replace')

        except Exception as e:
            self.logger.error(f"解码行数据失败: {str(e)}")
            return ""

    def _isValidDecodedText(self, text: str) -> bool:
        """验证解码后的文本是否合理"""
        try:
            if not text:
                return False

            # 检查是否包含过多的控制字符或无效字符
            controlCharCount = sum(1 for c in text if ord(c) < 32 and c not in '\r\n\t')
            if len(text) > 0 and controlCharCount / len(text) > 0.3:
                return False

            # 检查是否包含CSV相关的字符（逗号、引号等）
            if ',' in text or '"' in text or any(ord(c) >= 32 for c in text):
                return True

            return len(text.strip()) > 0

        except Exception:
            return False

    def _parseCsvRow(self, lineText: str, rowIndex: int) -> Dict[str, str]:
        """智能解析CSV行数据，按第一个逗号分割"""
        try:
            # 首先尝试智能分割方法
            result = self._smartColumnSplit(lineText)
            if result:
                testName, httpPayload = result
                if testName and httpPayload:
                    self.logger.debug(f"智能分割成功解析第 {rowIndex} 行")
                    return {
                        'testName': testName,
                        'httpPayload': httpPayload,
                        'rowIndex': rowIndex
                    }

            # 备选方案：标准CSV解析
            result = self._standardCsvParse(lineText)
            if result:
                testName, httpPayload = result
                if testName and httpPayload:
                    self.logger.debug(f"标准CSV解析成功解析第 {rowIndex} 行")
                    return {
                        'testName': testName,
                        'httpPayload': httpPayload,
                        'rowIndex': rowIndex
                    }

            self.logger.warning(f"第 {rowIndex} 行解析失败")
            return None

        except Exception as e:
            self.logger.error(f"解析第 {rowIndex} 行CSV数据失败: {str(e)}")
            return None

    def _smartColumnSplit(self, lineText: str) -> tuple:
        """智能列分割：按第一个非引号内的逗号分割"""
        try:
            if not lineText or not lineText.strip():
                return None

            # 查找第一个非引号内的逗号
            inQuotes = False
            escapeNext = False
            commaPos = -1

            for i, char in enumerate(lineText):
                if escapeNext:
                    escapeNext = False
                    continue
                elif char == '\\':
                    escapeNext = True
                elif char == '"':
                    inQuotes = not inQuotes
                elif char == ',' and not inQuotes:
                    commaPos = i
                    break

            if commaPos == -1:
                # 没有找到合适的逗号，尝试简单分割
                parts = lineText.split(',', 1)
                if len(parts) >= 2:
                    return self._cleanCsvField(parts[0]), self._cleanCsvField(parts[1])
                return None

            # 按找到的逗号位置分割
            testName = lineText[:commaPos]
            httpPayload = lineText[commaPos + 1:]

            return self._cleanCsvField(testName), self._cleanCsvField(httpPayload)

        except Exception as e:
            self.logger.debug(f"智能列分割失败: {str(e)}")
            return None

    def _standardCsvParse(self, lineText: str) -> tuple:
        """标准CSV解析方法"""
        try:
            import csv
            import io

            stringIo = io.StringIO(lineText)
            reader = csv.reader(stringIo, quoting=csv.QUOTE_MINIMAL)

            row = next(reader)
            if len(row) >= 2:
                return row[0].strip(), row[1].strip()

            return None

        except Exception as e:
            self.logger.debug(f"标准CSV解析失败: {str(e)}")
            return None

    def _cleanCsvField(self, field: str) -> str:
        """清理CSV字段，移除多余的引号和空白"""
        if not field:
            return ""

        field = field.strip()

        # 移除外层引号
        if len(field) >= 2 and field.startswith('"') and field.endswith('"'):
            field = field[1:-1]
            # 处理转义的双引号
            field = field.replace('""', '"')

        return field

    def _saveStandardCsvFile(self, csvData: List[Dict[str, str]], csvFilePath: str) -> bool:
        """以标准格式保存CSV文件，保持HTTP负载的原始格式"""
        try:
            import csv

            # 确保目录存在
            os.makedirs(os.path.dirname(csvFilePath), exist_ok=True)

            # 以UTF-8编码保存，保持原始数据格式
            with open(csvFilePath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['测试项目名称', 'HTTP负载']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)

                # 写入标题行
                writer.writeheader()

                # 写入数据行，保持HTTP负载的原始格式（不转义换行符）
                for item in csvData:
                    writer.writerow({
                        '测试项目名称': item['testName'],
                        'HTTP负载': item['httpPayload']  # 保持原始格式，包括换行符
                    })

            self.logger.info(f"成功以标准格式保存CSV文件: {csvFilePath}，共{len(csvData)}个测试项目")
            return True

        except Exception as e:
            self.logger.error(f"保存标准CSV文件失败: {str(e)}")
            return False

    def _parseCsvFile(self, csvFilePath: str) -> List[Dict[str, str]]:
        """解析CSV文件"""
        try:
            import csv
            import chardet
            csvData = []

            # 检测文件编码
            with open(csvFilePath, 'rb') as file:
                raw_data = file.read()
                encoding_result = chardet.detect(raw_data)
                encoding = encoding_result['encoding'] if encoding_result['encoding'] else 'utf-8'

            self.logger.info(f"检测到CSV文件编码: {encoding}")

            # 尝试多种编码方式打开文件
            encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']

            for enc in encodings_to_try:
                try:
                    with open(csvFilePath, 'r', encoding=enc) as file:
                        # 尝试检测CSV方言
                        sample = file.read(1024)
                        file.seek(0)
                        sniffer = csv.Sniffer()
                        try:
                            dialect = sniffer.sniff(sample)
                        except:
                            # 如果方言检测失败，使用默认方言
                            dialect = csv.excel

                        reader = csv.reader(file, dialect)

                        for rowIndex, row in enumerate(reader, 1):
                            if rowIndex == 1:
                                # 第一行是标题行，跳过
                                continue
                            if len(row) >= 2:
                                testName = row[0].strip()
                                httpPayload = row[1].strip()

                                if testName and httpPayload:
                                    csvData.append({
                                        'testName': testName,
                                        'httpPayload': httpPayload,
                                        'rowIndex': rowIndex
                                    })

                        self.logger.info(f"成功使用编码 {enc} 解析CSV文件，共{len(csvData)}个测试项目")
                        return csvData

                except UnicodeDecodeError:
                    self.logger.warning(f"编码 {enc} 解析失败，尝试下一种编码")
                    continue
                except Exception as e:
                    self.logger.warning(f"使用编码 {enc} 解析CSV文件时出错: {str(e)}")
                    continue

            # 如果所有编码都失败了
            self.logger.error("所有编码方式都无法解析CSV文件")
            return []

        except Exception as e:
            self.logger.error(f"解析CSV文件失败: {str(e)}")
            return []

    def _writeResultsToCsv(self, results: List[Dict[str, str]], csvFilePath: str) -> bool:
        """将测试结果写入CSV文件"""
        try:
            import csv

            if not results:
                self.logger.warning("没有结果数据需要写入CSV文件")
                return False

            # 确保目录存在
            os.makedirs(os.path.dirname(csvFilePath), exist_ok=True)

            # 获取CSV字段名（使用第一个结果的键作为字段名）
            fieldnames = list(results[0].keys())

            # 写入CSV文件，使用QUOTE_ALL确保包含换行符的字段被正确引用
            with open(csvFilePath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)

                # 写入标题行
                writer.writeheader()

                # 写入数据行，保持原始数据格式
                for result in results:
                    writer.writerow(result)

            self.logger.info(f"成功将 {len(results)} 条结果写入CSV文件: {csvFilePath}")
            return True

        except Exception as e:
            self.logger.error(f"写入结果CSV文件失败: {str(e)}")
            return False
