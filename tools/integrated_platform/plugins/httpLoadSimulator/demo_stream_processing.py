#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流式CSV处理演示 - 核心概念展示
"""

import io
import tempfile

def demo_stream_vs_traditional():
    """演示流式处理与传统处理的区别"""
    print("=== 流式处理 vs 传统处理演示 ===")
    print()
    
    # 创建测试数据
    csv_content = '''测试项目名称,HTTP负载
"JSON请求","POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"items\\":[1,2,3]}"
"Accept头测试","GET /api HTTP/1.1\\r\\nAccept: text/html,application/xml;q=0.9,*/*;q=0.8\\r\\n\\r\\n"
"文件上传","POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data\\r\\n\\r\\nfile,data,here"
"复杂Cookie","GET /api HTTP/1.1\\r\\nCookie: session=abc; user=test,admin; theme=dark\\r\\n\\r\\n"'''
    
    print("测试数据:")
    print(csv_content)
    print()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as f:
        f.write(csv_content)
        temp_file = f.name
    
    try:
        print("1. 传统处理方式（一次性读取）:")
        traditional_result = traditional_processing(temp_file)
        print(f"   结果: {len(traditional_result)} 个项目")
        print(f"   内存占用: 整个文件 + 解析结果")
        print()
        
        print("2. 流式处理方式（逐行读取）:")
        stream_result = stream_processing(temp_file)
        print(f"   结果: {len(stream_result)} 个项目")
        print(f"   内存占用: 8KB缓冲区 + 当前行")
        print()
        
        print("3. 结果对比:")
        for i, (trad, stream) in enumerate(zip(traditional_result, stream_result)):
            print(f"   项目 {i+1}:")
            print(f"     传统方式: {trad['testName']}")
            print(f"     流式方式: {stream['testName']}")
            print(f"     结果一致: {'✓' if trad['testName'] == stream['testName'] else '✗'}")
        
    finally:
        import os
        os.unlink(temp_file)

def traditional_processing(file_path):
    """传统的一次性读取处理"""
    print("   📖 一次性读取整个文件...")
    
    # 一次性读取整个文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("   🔍 分割行...")
    lines = content.split('\\n')
    
    print("   ⚙️ 处理所有行...")
    results = []
    for i, line in enumerate(lines[1:], 1):  # 跳过标题行
        if line.strip():
            result = parse_csv_line(line, i + 1)
            if result:
                results.append(result)
    
    print("   ✅ 传统处理完成")
    return results

def stream_processing(file_path):
    """流式逐行处理"""
    print("   🌊 开始流式处理...")
    
    results = []
    line_buffer = b''
    line_number = 0
    
    with open(file_path, 'rb') as f:
        print("   📦 逐块读取文件...")
        
        chunk_size = 64  # 小块大小用于演示
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                # 处理最后的缓冲区
                if line_buffer:
                    result = process_line_from_buffer(line_buffer, line_number + 1)
                    if result:
                        results.append(result)
                break
            
            line_buffer += chunk
            print(f"   📥 读取了 {len(chunk)} 字节，缓冲区大小: {len(line_buffer)}")
            
            # 处理完整的行
            while True:
                line_end = line_buffer.find(b'\\n')
                if line_end == -1:
                    break  # 没有完整的行
                
                # 提取完整的行
                line_bytes = line_buffer[:line_end]
                line_buffer = line_buffer[line_end + 1:]
                line_number += 1
                
                print(f"   🔍 处理第 {line_number} 行...")
                
                if line_number == 1:
                    print("   📋 跳过标题行")
                    continue
                
                # 处理数据行
                result = process_line_from_buffer(line_bytes, line_number)
                if result:
                    results.append(result)
                    print(f"   ✅ 第 {line_number} 行处理成功")
    
    print("   🎉 流式处理完成")
    return results

def process_line_from_buffer(line_bytes, line_number):
    """从缓冲区处理行数据"""
    try:
        # 解码
        line_text = line_bytes.decode('utf-8')
        
        # 解析
        return parse_csv_line(line_text, line_number)
        
    except Exception as e:
        print(f"   ⚠️ 第 {line_number} 行处理失败: {e}")
        return None

def parse_csv_line(line_text, line_number):
    """解析CSV行"""
    if not line_text.strip():
        return None
    
    # 智能分割（简化版）
    in_quotes = False
    comma_pos = -1
    
    for i, char in enumerate(line_text):
        if char == '"':
            in_quotes = not in_quotes
        elif char == ',' and not in_quotes:
            comma_pos = i
            break
    
    if comma_pos == -1:
        return None
    
    test_name = line_text[:comma_pos].strip().strip('"')
    http_payload = line_text[comma_pos + 1:].strip().strip('"')
    
    return {
        'testName': test_name,
        'httpPayload': http_payload,
        'rowIndex': line_number
    }

def demo_memory_efficiency():
    """演示内存效率"""
    print("\\n=== 内存效率演示 ===")
    print()
    
    # 模拟大文件处理
    print("模拟处理大型CSV文件:")
    print()
    
    print("传统方式:")
    print("  📁 文件大小: 100MB")
    print("  💾 内存占用: 100MB (原始) + 150MB (解析) = 250MB")
    print("  ⏱️ 处理时间: 一次性加载，然后快速处理")
    print("  ❌ 风险: 内存不足可能导致程序崩溃")
    print()
    
    print("流式处理:")
    print("  📁 文件大小: 100MB")
    print("  💾 内存占用: 8KB (缓冲) + 当前行大小 ≈ 10KB")
    print("  ⏱️ 处理时间: 边读边处理，总时间略长")
    print("  ✅ 优势: 内存占用恒定，支持任意大小文件")
    print()
    
    print("结论:")
    print("  🎯 小文件(<10MB): 传统方式更快")
    print("  🎯 大文件(>10MB): 流式处理更稳定")
    print("  🎯 超大文件(>100MB): 只能用流式处理")

def demo_error_handling():
    """演示错误处理"""
    print("\\n=== 错误处理演示 ===")
    print()
    
    # 创建包含错误的测试数据
    problematic_csv = '''测试项目名称,HTTP负载
"正常行","GET /api HTTP/1.1\\r\\n\\r\\n"
"错误行1,缺少引号,"POST /api HTTP/1.1
"正常行2","POST /data HTTP/1.1\\r\\n\\r\\n"
错误行2,没有引号,GET /test HTTP/1.1
"正常行3","GET /final HTTP/1.1\\r\\n\\r\\n"'''
    
    print("包含错误的CSV数据:")
    for i, line in enumerate(problematic_csv.split('\\n'), 1):
        status = "✅" if i == 1 or "正常行" in line else "❌"
        print(f"  行 {i}: {status} {line}")
    print()
    
    print("流式处理错误处理:")
    print("  🔍 逐行验证，精确定位错误")
    print("  📝 详细的错误日志记录")
    print("  🔄 错误恢复，继续处理后续行")
    print("  📊 最终统计：成功/失败行数")
    print()
    
    print("优势:")
    print("  ✅ 不会因为单行错误而停止整个处理")
    print("  ✅ 提供精确的错误行号和错误原因")
    print("  ✅ 可以收集所有错误信息供用户修正")

if __name__ == '__main__':
    demo_stream_vs_traditional()
    demo_memory_efficiency()
    demo_error_handling()
