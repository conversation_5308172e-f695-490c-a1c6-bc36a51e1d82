<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV文本上传示例</title>
    <style>
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .preview-area {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .csv-preview {
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .submit-btn:hover {
            background-color: #218838;
        }
        .submit-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info-panel {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSV文件文本上传</h1>
        <p>支持直接读取CSV文件内容为文本，避免编码问题</p>

        <!-- 文件上传区域 -->
        <div class="upload-area" id="uploadArea">
            <p>拖拽CSV文件到此处，或点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择CSV文件
            </button>
            <input type="file" id="fileInput" class="file-input" accept=".csv" />
        </div>

        <!-- 文件信息 -->
        <div class="info-panel" id="fileInfo" style="display: none;">
            <h3>文件信息</h3>
            <p><strong>文件名:</strong> <span id="fileName"></span></p>
            <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
            <p><strong>检测到的行数:</strong> <span id="lineCount"></span></p>
            <p><strong>检测到的列数:</strong> <span id="columnCount"></span></p>
        </div>

        <!-- CSV预览 -->
        <div class="preview-area" id="previewArea" style="display: none;">
            <h3>CSV内容预览 (前10行)</h3>
            <div class="csv-preview" id="csvPreview"></div>
        </div>

        <!-- 操作按钮 -->
        <div id="actionButtons" style="display: none;">
            <button class="submit-btn" id="submitBtn" onclick="submitCsvData()">
                提交到后台
            </button>
            <button class="submit-btn" onclick="clearData()" style="background-color: #6c757d;">
                清除
            </button>
        </div>

        <!-- 结果显示 -->
        <div id="resultArea"></div>
    </div>

    <script>
        let csvTextContent = '';
        let csvData = [];

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                showError('请选择CSV文件');
                return;
            }

            // 使用FileReader以文本方式读取文件
            const reader = new FileReader();
            
            reader.onload = function(e) {
                csvTextContent = e.target.result;
                processCSVText(file);
            };
            
            reader.onerror = function() {
                showError('文件读取失败');
            };
            
            // 以文本方式读取，让浏览器自动处理编码
            reader.readAsText(file, 'UTF-8');
        }

        function processCSVText(file) {
            try {
                // 解析CSV文本
                csvData = parseCSVText(csvTextContent);
                
                // 显示文件信息
                showFileInfo(file, csvData);
                
                // 显示预览
                showPreview(csvData);
                
                // 显示操作按钮
                document.getElementById('actionButtons').style.display = 'block';
                
                clearMessages();
                
            } catch (error) {
                showError('CSV文件解析失败: ' + error.message);
            }
        }

        function parseCSVText(text) {
            const lines = text.split('\n');
            const result = [];
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line) {
                    // 简单的CSV解析（这里可以使用更复杂的CSV解析库）
                    const row = parseCSVLine(line);
                    result.push({
                        rowIndex: i + 1,
                        data: row,
                        rawLine: line
                    });
                }
            }
            
            return result;
        }

        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                
                if (char === '"') {
                    if (inQuotes && line[i + 1] === '"') {
                        // 转义的引号
                        current += '"';
                        i++; // 跳过下一个引号
                    } else {
                        // 切换引号状态
                        inQuotes = !inQuotes;
                    }
                } else if (char === ',' && !inQuotes) {
                    // 字段分隔符
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            
            // 添加最后一个字段
            result.push(current.trim());
            
            return result;
        }

        function showFileInfo(file, data) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('lineCount').textContent = data.length;
            
            const columnCount = data.length > 0 ? data[0].data.length : 0;
            document.getElementById('columnCount').textContent = columnCount;
            
            document.getElementById('fileInfo').style.display = 'block';
        }

        function showPreview(data) {
            const preview = document.getElementById('csvPreview');
            const previewLines = data.slice(0, 10); // 只显示前10行
            
            let previewText = '';
            previewLines.forEach((row, index) => {
                previewText += `行 ${row.rowIndex}: ${row.rawLine}\n`;
                
                // 如果是数据行，显示解析后的字段
                if (index > 0 && row.data.length >= 2) {
                    previewText += `  -> 测试名称: ${row.data[0]}\n`;
                    previewText += `  -> HTTP负载: ${row.data[1].substring(0, 100)}${row.data[1].length > 100 ? '...' : ''}\n`;
                }
                previewText += '\n';
            });
            
            preview.textContent = previewText;
            document.getElementById('previewArea').style.display = 'block';
        }

        function submitCsvData() {
            if (!csvTextContent) {
                showError('没有CSV数据可提交');
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            // 准备提交数据
            const submitData = {
                csvText: csvTextContent,
                fileName: document.getElementById('fileName').textContent,
                lineCount: csvData.length,
                timestamp: new Date().toISOString()
            };

            // 发送到后台
            fetch('/api/plugins/httpLoadSimulator/upload-csv-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(`CSV文件提交成功！解析了 ${data.count} 个测试项目`);
                } else {
                    showError('提交失败: ' + data.message);
                }
            })
            .catch(error => {
                showError('网络错误: ' + error.message);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = '提交到后台';
            });
        }

        function clearData() {
            csvTextContent = '';
            csvData = [];
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('previewArea').style.display = 'none';
            document.getElementById('actionButtons').style.display = 'none';
            clearMessages();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showError(message) {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = `<div class="error">${message}</div>`;
        }

        function showSuccess(message) {
            const resultArea = document.getElementById('resultArea');
            resultArea.innerHTML = `<div class="success">${message}</div>`;
        }

        function clearMessages() {
            document.getElementById('resultArea').innerHTML = '';
        }
    </script>
</body>
</html>
