<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数定义修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        .btn-success {
            background-color: #48bb78;
            color: white;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8fafc;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JavaScript函数定义修复测试</h1>
        <p>测试修复后的JavaScript函数是否正确定义和可调用。</p>

        <!-- 测试1：函数存在性检查 -->
        <div class="test-section">
            <div class="test-title">测试1：函数存在性检查</div>
            
            <button class="btn btn-primary" onclick="testFunctionExistence()">
                检查函数是否存在
            </button>
            
            <div id="testResult1" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试2：CSV文本处理测试 -->
        <div class="test-section">
            <div class="test-title">测试2：CSV文本处理功能测试</div>
            
            <div class="upload-area">
                <input type="file" id="testFile" accept=".csv">
                <p>选择CSV文件测试文本处理功能</p>
            </div>
            
            <button class="btn btn-primary" onclick="testCsvTextProcessing()">
                测试CSV文本处理
            </button>
            
            <div id="testResult2" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试3：模拟文件读取测试 -->
        <div class="test-section">
            <div class="test-title">测试3：模拟文件读取测试</div>
            
            <button class="btn btn-primary" onclick="testMockFileReading()">
                模拟文件读取流程
            </button>
            
            <div id="testResult3" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试4：综合测试 -->
        <div class="test-section">
            <div class="test-title">测试4：综合功能测试</div>
            
            <button class="btn btn-success" onclick="runAllTests()">
                运行所有测试
            </button>
            
            <div id="testResult4" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟全局变量
        var csvTextContent = null;
        var csvTextData = [];

        // 模拟必要的函数
        function showAlert(message, type) {
            console.log('[' + type.toUpperCase() + '] ' + message);
            
            // 显示在页面上
            var alertDiv = document.createElement('div');
            alertDiv.style.cssText = 'margin: 10px 0; padding: 10px; border-radius: 4px; ' +
                (type === 'error' ? 'background: #f8d7da; color: #721c24;' : 'background: #d4edda; color: #155724;');
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);
            
            setTimeout(function() {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }

        function showCsvTextFileInfo(file, csvData) {
            console.log('显示CSV文件信息:', file.name, csvData.length + '行');
        }

        function showCsvTextPreview(csvData) {
            console.log('显示CSV预览:', csvData.length + '行数据');
        }

        function parseCsvText(text) {
            // 简单的CSV解析模拟
            var lines = text.split('\n');
            var result = [];
            
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line) {
                    result.push({
                        rowIndex: i + 1,
                        data: line.split(','),
                        rawLine: line
                    });
                }
            }
            
            return result;
        }

        function testFunctionExistence() {
            var functions = [
                'toggleInputMode',
                'toggleCsvUploadMode', 
                'handleCsvFileSelect',
                'handleCsvTextFileSelect',
                'processCsvText',
                'resetCsvConfig',
                'resetCsvTextConfig'
            ];
            
            var results = [];
            var allExist = true;
            
            for (var i = 0; i < functions.length; i++) {
                var funcName = functions[i];
                var exists = typeof window[funcName] === 'function';
                results.push(funcName + ': ' + (exists ? '✅ 存在' : '❌ 不存在'));
                if (!exists) allExist = false;
            }
            
            var resultDiv = document.getElementById('testResult1');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result ' + (allExist ? 'success' : 'error');
            resultDiv.innerHTML = '函数存在性检查结果:\n' + results.join('\n') + 
                '\n\n总体结果: ' + (allExist ? '✅ 所有函数都存在' : '❌ 部分函数缺失');
        }

        function testCsvTextProcessing() {
            var file = document.getElementById('testFile').files[0];
            var resultDiv = document.getElementById('testResult2');
            
            if (!file) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '请先选择一个CSV文件';
                return;
            }
            
            // 测试processCsvText函数是否存在
            if (typeof window.processCsvText !== 'function') {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ processCsvText函数不存在';
                return;
            }
            
            // 模拟文件读取
            var reader = new FileReader();
            reader.onload = function(e) {
                try {
                    csvTextContent = e.target.result;
                    
                    // 调用processCsvText函数
                    window.processCsvText(file);
                    
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = 
                        'CSV文本处理测试结果:\n' +
                        '✅ processCsvText函数调用成功\n' +
                        '✅ 文件名: ' + file.name + '\n' +
                        '✅ 文件大小: ' + file.size + ' 字节\n' +
                        '✅ 文本长度: ' + csvTextContent.length + ' 字符\n' +
                        '✅ 解析数据: ' + csvTextData.length + ' 行';
                        
                } catch (error) {
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ processCsvText函数调用失败:\n' + error.message;
                }
            };
            
            reader.onerror = function() {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 文件读取失败';
            };
            
            reader.readAsText(file);
        }

        function testMockFileReading() {
            var resultDiv = document.getElementById('testResult3');
            
            try {
                // 模拟CSV内容
                var mockCsvContent = '测试项目名称,HTTP负载\n' +
                    '"GET请求","GET /api HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n"\n' +
                    '"POST请求","POST /data HTTP/1.1\\r\\nContent-Type: json\\r\\n\\r\\n{data}"';
                
                // 模拟文件对象
                var mockFile = {
                    name: 'test.csv',
                    size: mockCsvContent.length,
                    type: 'text/csv'
                };
                
                // 设置全局变量
                csvTextContent = mockCsvContent;
                
                // 调用processCsvText
                if (typeof window.processCsvText === 'function') {
                    window.processCsvText(mockFile);
                    
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = 
                        '模拟文件读取测试结果:\n' +
                        '✅ 模拟文件创建成功\n' +
                        '✅ processCsvText函数调用成功\n' +
                        '✅ 模拟文件名: ' + mockFile.name + '\n' +
                        '✅ 模拟内容长度: ' + mockCsvContent.length + ' 字符\n' +
                        '✅ 解析结果: ' + csvTextData.length + ' 行数据';
                } else {
                    throw new Error('processCsvText函数不存在');
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 模拟文件读取测试失败:\n' + error.message;
            }
        }

        function runAllTests() {
            var resultDiv = document.getElementById('testResult4');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = '正在运行所有测试...';
            
            setTimeout(function() {
                testFunctionExistence();
            }, 100);
            
            setTimeout(function() {
                testMockFileReading();
            }, 200);
            
            setTimeout(function() {
                var allResults = [];
                
                // 检查函数存在性
                var functions = ['processCsvText', 'handleCsvTextFileSelect', 'toggleCsvUploadMode'];
                var functionsOk = true;
                for (var i = 0; i < functions.length; i++) {
                    if (typeof window[functions[i]] !== 'function') {
                        functionsOk = false;
                        break;
                    }
                }
                allResults.push('函数存在性: ' + (functionsOk ? '✅ 通过' : '❌ 失败'));
                
                // 检查模拟处理
                var processingOk = csvTextData.length > 0;
                allResults.push('CSV处理: ' + (processingOk ? '✅ 通过' : '❌ 失败'));
                
                // 检查错误处理
                var errorHandlingOk = typeof showAlert === 'function';
                allResults.push('错误处理: ' + (errorHandlingOk ? '✅ 通过' : '❌ 失败'));
                
                var overallSuccess = functionsOk && processingOk && errorHandlingOk;
                
                resultDiv.className = 'test-result ' + (overallSuccess ? 'success' : 'error');
                resultDiv.innerHTML = 
                    '综合测试结果:\n' + 
                    allResults.join('\n') + 
                    '\n\n总体结果: ' + (overallSuccess ? '✅ 所有测试通过' : '❌ 部分测试失败') +
                    '\n\n修复状态: ' + (overallSuccess ? '✅ 函数定义问题已修复' : '❌ 仍有问题需要解决');
                    
            }, 500);
        }
    </script>
    
    <!-- 引入实际的JavaScript文件 -->
    <script src="static/js/create_task.js"></script>
</body>
</html>
