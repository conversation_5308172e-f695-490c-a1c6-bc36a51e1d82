#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV中特殊字符处理的核心功能
"""

def test_smart_column_split():
    """测试智能列分割的核心功能"""
    print("=== 测试智能列分割功能 ===")
    print()
    
    # 测试用例：各种包含特殊字符的CSV行
    test_cases = [
        # 基本情况
        {
            'input': 'JSON请求测试,POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{"name":"test"}',
            'expected_name': 'JSON请求测试',
            'description': '基本情况：无引号'
        },
        
        # 包含引号的情况
        {
            'input': '"JSON请求测试","POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"value\\":123}"',
            'expected_name': 'JSON请求测试',
            'description': '标准引号格式'
        },
        
        # 测试名称包含逗号
        {
            'input': '"测试,包含逗号","POST /api HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n"',
            'expected_name': '测试,包含逗号',
            'description': '测试名称包含逗号'
        },
        
        # HTTP负载包含逗号
        {
            'input': '"用户代理测试","GET /api HTTP/1.1\\r\\nUser-Agent: Mozilla/5.0 (compatible; bot, crawler)\\r\\n\\r\\n"',
            'expected_name': '用户代理测试',
            'description': 'HTTP负载包含逗号'
        },
        
        # 复杂JSON负载
        {
            'input': '"复杂JSON","POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"data\\":{\\"items\\":[1,2,3],\\"status\\":\\"ok\\"}}"',
            'expected_name': '复杂JSON',
            'description': '复杂JSON负载'
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"测试用例 {i+1}: {test_case['description']}")
        print(f"输入: {test_case['input']}")
        
        result = smart_column_split(test_case['input'])
        if result:
            test_name, http_payload = result
            print(f"✓ 解析成功")
            print(f"  测试名称: '{test_name}'")
            print(f"  期望名称: '{test_case['expected_name']}'")
            print(f"  名称匹配: {'✓' if test_name == test_case['expected_name'] else '✗'}")
            print(f"  HTTP负载长度: {len(http_payload)} 字符")
            print(f"  HTTP负载预览: {http_payload[:80]}...")
        else:
            print("✗ 解析失败")
        print()

def smart_column_split(line_text: str):
    """智能列分割实现"""
    if not line_text or not line_text.strip():
        return None
    
    # 查找第一个非引号内的逗号
    in_quotes = False
    escape_next = False
    comma_pos = -1
    
    i = 0
    while i < len(line_text):
        char = line_text[i]
        
        if escape_next:
            escape_next = False
        elif char == '\\\\':
            escape_next = True
        elif char == '"':
            in_quotes = not in_quotes
        elif char == ',' and not in_quotes:
            comma_pos = i
            break
        
        i += 1
    
    if comma_pos == -1:
        # 没有找到合适的逗号，尝试简单分割
        parts = line_text.split(',', 1)
        if len(parts) >= 2:
            return clean_csv_field(parts[0]), clean_csv_field(parts[1])
        return None
    
    # 按找到的逗号位置分割
    test_name = line_text[:comma_pos]
    http_payload = line_text[comma_pos + 1:]
    
    return clean_csv_field(test_name), clean_csv_field(http_payload)

def clean_csv_field(field: str) -> str:
    """清理CSV字段"""
    if not field:
        return ""
    
    field = field.strip()
    
    # 移除外层引号
    if len(field) >= 2 and field.startswith('"') and field.endswith('"'):
        field = field[1:-1]
        # 处理转义的双引号
        field = field.replace('""', '"')
    
    return field

def test_problematic_cases():
    """测试问题案例"""
    print("=== 测试问题案例 ===")
    print()
    
    # 真实的问题案例
    problematic_cases = [
        # 包含多个逗号的复杂HTTP请求
        '"Accept头测试","GET /api HTTP/1.1\\r\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\\r\\n\\r\\n"',
        
        # 包含JSON数组的POST请求
        '"JSON数组","POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"items\\":[{\\"id\\":1,\\"name\\":\\"test\\"},{\\"id\\":2,\\"name\\":\\"demo\\"}]}"',
        
        # 包含Cookie的复杂请求
        '"Cookie测试","GET /api HTTP/1.1\\r\\nCookie: session=abc123; user=test,admin; theme=dark\\r\\n\\r\\n"',
        
        # multipart表单数据
        '"文件上传","POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data; boundary=----WebKit\\r\\n\\r\\n------WebKit\\r\\nContent-Disposition: form-data; name=\\"file\\", filename=\\"test.txt\\"\\r\\n\\r\\nfile,content,here\\r\\n------WebKit--"'
    ]
    
    for i, case in enumerate(problematic_cases):
        print(f"问题案例 {i+1}:")
        print(f"原始数据: {case}")
        
        result = smart_column_split(case)
        if result:
            test_name, http_payload = result
            print(f"✓ 解析成功")
            print(f"  测试名称: '{test_name}'")
            print(f"  HTTP负载: {http_payload[:100]}...")
            
            # 验证HTTP负载中的逗号是否保持完整
            comma_count = http_payload.count(',')
            print(f"  HTTP负载中的逗号数量: {comma_count}")
        else:
            print("✗ 解析失败")
        print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    print()
    
    edge_cases = [
        # 空字段
        '"",""',
        
        # 只有一个字段
        '"单字段测试"',
        
        # 不平衡的引号
        '"测试,"POST /api HTTP/1.1"',
        
        # 嵌套引号
        '"测试\\"内部引号\\"","GET /api HTTP/1.1\\r\\n\\r\\n"',
        
        # 多个连续逗号
        '"测试",,,"GET /api HTTP/1.1"',
        
        # 中文逗号
        '"测试，中文逗号","GET /api HTTP/1.1\\r\\n\\r\\n"'
    ]
    
    for i, case in enumerate(edge_cases):
        print(f"边界情况 {i+1}: {case}")
        result = smart_column_split(case)
        if result:
            test_name, http_payload = result
            print(f"  ✓ 测试名称: '{test_name}', HTTP负载: '{http_payload[:50]}...'")
        else:
            print("  ✗ 解析失败")
        print()

if __name__ == '__main__':
    test_smart_column_split()
    test_problematic_cases()
    test_edge_cases()
