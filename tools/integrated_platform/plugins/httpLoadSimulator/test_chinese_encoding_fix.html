<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文编码显示修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8fafc;
            margin: 15px 0;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        .btn-success {
            background-color: #48bb78;
            color: white;
        }
        .btn-warning {
            background-color: #ed8936;
            color: white;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
        }
        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .encoding-info {
            background: #e6f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
        }
        .preview-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSV中文编码显示修复测试</h1>
        <p>测试修复后的前台编码检测功能，确保中文内容正确显示。</p>

        <!-- 测试1：创建测试文件 -->
        <div class="test-section">
            <div class="test-title">测试1：创建不同编码的测试文件</div>
            
            <button class="btn btn-primary" onclick="createTestFiles()">
                创建测试文件
            </button>
            
            <div id="testResult1" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试2：编码检测测试 -->
        <div class="test-section">
            <div class="test-title">测试2：编码检测功能测试</div>
            
            <div class="upload-area">
                <input type="file" id="encodingTestFile" accept=".csv">
                <p>选择CSV文件测试编码检测</p>
            </div>
            
            <button class="btn btn-primary" onclick="testEncodingDetection()">
                测试编码检测
            </button>
            
            <div id="testResult2" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试3：中文显示测试 -->
        <div class="test-section">
            <div class="test-title">测试3：中文内容显示测试</div>
            
            <div class="upload-area">
                <input type="file" id="chineseTestFile" accept=".csv">
                <p>选择包含中文的CSV文件测试显示效果</p>
            </div>
            
            <button class="btn btn-primary" onclick="testChineseDisplay()">
                测试中文显示
            </button>
            
            <div id="testResult3" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试4：对比测试 -->
        <div class="test-section">
            <div class="test-title">测试4：修复前后对比测试</div>
            
            <div class="upload-area">
                <input type="file" id="compareTestFile" accept=".csv">
                <p>选择CSV文件进行修复前后对比</p>
            </div>
            
            <button class="btn btn-warning" onclick="testOldMethod()">
                旧方法（UTF-8强制）
            </button>
            <button class="btn btn-success" onclick="testNewMethod()">
                新方法（智能检测）
            </button>
            
            <div id="testResult4" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟全局变量
        var csvTextContent = null;
        var csvTextData = [];

        // 模拟必要的函数
        function showAlert(message, type) {
            console.log('[' + type.toUpperCase() + '] ' + message);
        }

        function showCsvTextFileInfo(file, csvData) {
            console.log('显示CSV文件信息:', file.name, csvData.length + '行');
        }

        function showCsvTextPreview(csvData) {
            console.log('显示CSV预览:', csvData.length + '行数据');
        }

        function parseCsvText(text) {
            var lines = text.split('\n');
            var result = [];
            
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line) {
                    result.push({
                        rowIndex: i + 1,
                        data: line.split(','),
                        rawLine: line
                    });
                }
            }
            
            return result;
        }

        function createTestFiles() {
            var resultDiv = document.getElementById('testResult1');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            
            var testContent = '测试项目名称,HTTP负载\n' +
                '"GET请求测试","GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: Mozilla/5.0\\r\\n\\r\\n"\n' +
                '"POST请求测试","POST /api/data HTTP/1.1\\r\\nHost: example.com\\r\\nContent-Type: application/json\\r\\nContent-Length: 25\\r\\n\\r\\n{\\"key\\": \\"value\\"}"\n' +
                '"中文路径测试","GET /中文路径 HTTP/1.1\\r\\nHost: 测试服务器.com\\r\\n\\r\\n"';
            
            resultDiv.innerHTML = 
                '创建的测试文件内容:\n' +
                '===================\n' +
                testContent + '\n\n' +
                '说明:\n' +
                '- 包含中文项目名称\n' +
                '- 包含中文HTTP请求内容\n' +
                '- 可以保存为不同编码的CSV文件进行测试\n' +
                '- 建议保存为GBK编码测试编码检测功能';
        }

        function testEncodingDetection() {
            var file = document.getElementById('encodingTestFile').files[0];
            var resultDiv = document.getElementById('testResult2');
            
            if (!file) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '请先选择一个CSV文件';
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在检测文件编码...';
            
            // 检测编码
            if (typeof window.detectFileEncodingAndRead === 'function') {
                window.detectFileEncodingAndRead(file, function(content, encoding) {
                    if (content) {
                        var preview = content.substring(0, 500);
                        if (content.length > 500) preview += '...';
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = 
                            '编码检测结果:\n' +
                            '=============\n' +
                            '文件名: ' + file.name + '\n' +
                            '文件大小: ' + file.size + ' 字节\n' +
                            '检测编码: ' + encoding + '\n' +
                            '内容长度: ' + content.length + ' 字符\n\n' +
                            '内容预览:\n' +
                            '=========\n' +
                            preview;
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.innerHTML = '编码检测失败，无法读取文件内容';
                    }
                });
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '编码检测函数不存在，请检查JavaScript文件是否正确加载';
            }
        }

        function testChineseDisplay() {
            var file = document.getElementById('chineseTestFile').files[0];
            var resultDiv = document.getElementById('testResult3');
            
            if (!file) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '请先选择一个CSV文件';
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在测试中文显示...';
            
            if (typeof window.detectFileEncodingAndRead === 'function') {
                window.detectFileEncodingAndRead(file, function(content, encoding) {
                    if (content) {
                        // 解析CSV内容
                        var lines = content.split('\n');
                        var chineseCharCount = 0;
                        var totalCharCount = 0;
                        
                        for (var i = 0; i < content.length; i++) {
                            var charCode = content.charCodeAt(i);
                            if (charCode > 127) {
                                totalCharCount++;
                                if (charCode >= 0x4e00 && charCode <= 0x9fff) {
                                    chineseCharCount++;
                                }
                            }
                        }
                        
                        var chineseRatio = totalCharCount > 0 ? (chineseCharCount / totalCharCount) : 0;
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = 
                            '中文显示测试结果:\n' +
                            '================\n' +
                            '文件名: ' + file.name + '\n' +
                            '使用编码: ' + encoding + '\n' +
                            '总行数: ' + lines.length + '\n' +
                            '中文字符数: ' + chineseCharCount + '\n' +
                            '非ASCII字符数: ' + totalCharCount + '\n' +
                            '中文字符比例: ' + (chineseRatio * 100).toFixed(1) + '%\n\n' +
                            '前5行内容:\n' +
                            '==========\n' +
                            lines.slice(0, 5).join('\n');
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.innerHTML = '文件读取失败';
                    }
                });
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '编码检测函数不存在';
            }
        }

        function testOldMethod() {
            var file = document.getElementById('compareTestFile').files[0];
            var resultDiv = document.getElementById('testResult4');
            
            if (!file) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '请先选择一个CSV文件';
                return;
            }
            
            // 使用旧方法：强制UTF-8
            var reader = new FileReader();
            reader.onload = function(e) {
                var content = e.target.result;
                var preview = content.substring(0, 300);
                
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = 
                    '旧方法测试结果 (强制UTF-8):\n' +
                    '===========================\n' +
                    '文件名: ' + file.name + '\n' +
                    '强制编码: UTF-8\n' +
                    '内容长度: ' + content.length + ' 字符\n\n' +
                    '内容预览:\n' +
                    '=========\n' +
                    preview + '\n\n' +
                    '注意: 如果文件不是UTF-8编码，中文可能显示为乱码';
            };
            reader.readAsText(file, 'UTF-8');
        }

        function testNewMethod() {
            var file = document.getElementById('compareTestFile').files[0];
            var resultDiv = document.getElementById('testResult4');
            
            if (!file) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '请先选择一个CSV文件';
                return;
            }
            
            if (typeof window.detectFileEncodingAndRead === 'function') {
                window.detectFileEncodingAndRead(file, function(content, encoding) {
                    if (content) {
                        var preview = content.substring(0, 300);
                        
                        var existingContent = resultDiv.innerHTML;
                        resultDiv.style.display = 'block';
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = existingContent + '\n\n' +
                            '新方法测试结果 (智能检测):\n' +
                            '===========================\n' +
                            '文件名: ' + file.name + '\n' +
                            '检测编码: ' + encoding + '\n' +
                            '内容长度: ' + content.length + ' 字符\n\n' +
                            '内容预览:\n' +
                            '=========\n' +
                            preview + '\n\n' +
                            '优势: 自动检测编码，中文显示正确';
                    } else {
                        resultDiv.innerHTML += '\n\n新方法测试失败';
                    }
                });
            } else {
                resultDiv.innerHTML += '\n\n新方法函数不存在';
            }
        }
    </script>
    
    <!-- 引入实际的JavaScript文件 -->
    <script src="static/js/create_task.js"></script>
</body>
</html>
