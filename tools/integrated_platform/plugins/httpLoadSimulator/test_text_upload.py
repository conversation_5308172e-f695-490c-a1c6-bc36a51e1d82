#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV文本上传功能
"""

import json
import requests
import tempfile
import os

def test_csv_text_upload():
    """测试CSV文本上传功能"""
    print("=== 测试CSV文本上传功能 ===")
    print()
    
    # 创建测试CSV文本内容
    csv_text = '''测试项目名称,HTTP负载
"JSON请求测试","POST /api/data HTTP/1.1\r\nContent-Type: application/json\r\nContent-Length: 45\r\n\r\n{""name"":""test"",""value"":123,""status"":""active""}"
"文件上传测试","POST /upload HTTP/1.1\r\nContent-Type: multipart/form-data; boundary=----WebKit\r\nContent-Length: 234\r\n\r\n------WebKit\r\nContent-Disposition: form-data; name=""file""; filename=""test.txt""\r\nContent-Type: text/plain\r\n\r\nHello World!\r\n------WebKit--"
"复杂Accept头测试","GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n\r\n"
"中文测试,包含逗号","POST /中文路径 HTTP/1.1\r\nHost: 测试服务器.com\r\nContent-Type: application/json; charset=utf-8\r\n\r\n{""消息"":""你好,世界!"",""状态"":""成功""}"'''
    
    print("1. 测试CSV文本内容:")
    print(f"   文本长度: {len(csv_text)} 字符")
    print(f"   行数: {len(csv_text.split(chr(10)))}")
    has_crlf = '\\r\\n' in csv_text
    has_comma = ',' in csv_text
    print(f"   包含\\r\\n: {'✓' if has_crlf else '✗'}")
    print(f"   包含逗号: {'✓' if has_comma else '✗'}")
    print()
    
    # 准备上传数据
    upload_data = {
        'csvText': csv_text,
        'fileName': 'test_upload.csv',
        'lineCount': len(csv_text.split('\n')),
        'timestamp': '2024-01-01T12:00:00Z'
    }
    
    print("2. 模拟前台上传:")
    print(f"   文件名: {upload_data['fileName']}")
    print(f"   数据大小: {len(json.dumps(upload_data))} 字节")
    print()
    
    # 测试本地解析功能
    print("3. 本地解析测试:")
    csv_data = parse_csv_text_local(csv_text)
    print(f"   解析结果: {len(csv_data)} 个测试项目")
    
    for i, item in enumerate(csv_data):
        print(f"   项目 {i+1}:")
        print(f"     名称: {item['testName']}")
        print(f"     HTTP负载长度: {len(item['httpPayload'])} 字符")
        has_crlf = '\r\n' in item['httpPayload']
        has_comma = ',' in item['httpPayload']
        print(f"     包含\\r\\n: {'✓' if has_crlf else '✗'}")
        print(f"     包含逗号: {'✓' if has_comma else '✗'}")
        print(f"     负载预览: {item['httpPayload'][:80]}...")
        print()

def parse_csv_text_local(csv_text):
    """本地解析CSV文本"""
    import csv
    import io
    
    csv_data = []
    
    try:
        # 使用StringIO创建文件对象
        string_io = io.StringIO(csv_text)
        
        # 创建CSV读取器
        reader = csv.reader(string_io)
        
        # 处理每一行
        for row_index, row in enumerate(reader, 1):
            if row_index == 1:
                # 跳过标题行
                print(f"   标题行: {row}")
                continue
            
            if len(row) >= 2:
                test_name = row[0].strip()
                http_payload = row[1].strip()
                
                if test_name and http_payload:
                    csv_data.append({
                        'testName': test_name,
                        'httpPayload': http_payload,
                        'rowIndex': row_index
                    })
                    print(f"   ✓ 第 {row_index} 行解析成功")
                else:
                    print(f"   ✗ 第 {row_index} 行数据不完整")
            else:
                print(f"   ✗ 第 {row_index} 行列数不足: {len(row)}")
        
        return csv_data
        
    except Exception as e:
        print(f"   ✗ 解析失败: {str(e)}")
        return []

def test_file_vs_text_comparison():
    """对比文件上传和文本上传的差异"""
    print("=== 文件上传 vs 文本上传对比 ===")
    print()
    
    # 创建测试内容
    test_content = '''测试项目名称,HTTP负载
"编码测试","GET /api HTTP/1.1\r\nHost: 测试服务器.com\r\n\r\n"
"特殊字符","POST /data HTTP/1.1\r\nContent-Type: application/json\r\n\r\n{""test"":""数据,包含逗号""}"'''
    
    print("1. 文件上传方式:")
    print("   优点:")
    print("     - 保持原始二进制格式")
    print("     - 支持各种编码")
    print("   缺点:")
    print("     - 编码检测复杂")
    print("     - 混合编码难处理")
    print("     - 行分割容易出错")
    print()
    
    print("2. 文本上传方式:")
    print("   优点:")
    print("     - 浏览器自动处理编码")
    print("     - 标准CSV库正确解析")
    print("     - 避免行分割错误")
    print("     - 实现简单可靠")
    print("   缺点:")
    print("     - 依赖浏览器编码处理")
    print("     - 可能丢失原始编码信息")
    print()
    
    print("3. 推荐方案:")
    print("   ✓ 使用文本上传方式")
    print("   ✓ 前台FileReader.readAsText()自动处理编码")
    print("   ✓ 后台直接解析文本，避免编码问题")
    print("   ✓ 使用标准CSV库，确保解析正确")

def test_browser_encoding_handling():
    """测试浏览器编码处理能力"""
    print("=== 浏览器编码处理测试 ===")
    print()
    
    print("FileReader.readAsText() 的编码处理:")
    print("1. 自动检测文件编码")
    print("2. 转换为JavaScript字符串（UTF-16）")
    print("3. 传输时使用UTF-8编码")
    print("4. 后台接收UTF-8文本")
    print()
    
    print("优势:")
    print("✓ 浏览器内置编码检测比chardet更准确")
    print("✓ 自动处理BOM标记")
    print("✓ 支持用户指定编码")
    print("✓ 统一输出UTF-8格式")
    print()
    
    print("示例代码:")
    print("""
    // 前台JavaScript
    const reader = new FileReader();
    reader.onload = function(e) {
        const csvText = e.target.result;  // 已经是正确的文本
        // 发送到后台
        fetch('/api/upload-csv-text', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({csvText: csvText})
        });
    };
    reader.readAsText(file, 'UTF-8');  // 可选指定编码
    """)

def test_advantages_summary():
    """总结文本上传方案的优势"""
    print("=== 文本上传方案优势总结 ===")
    print()
    
    advantages = [
        {
            "问题": "混合编码处理",
            "文件上传": "❌ 需要复杂的编码检测和分段处理",
            "文本上传": "✅ 浏览器自动统一为UTF-8"
        },
        {
            "问题": "行分割错误",
            "文件上传": "❌ HTTP负载中的\\r\\n被误判为行分隔符",
            "文本上传": "✅ 标准CSV库正确处理引号内的换行符"
        },
        {
            "问题": "特殊字符处理",
            "文件上传": "❌ 逗号、引号等字符干扰解析",
            "文本上传": "✅ CSV库标准处理所有特殊字符"
        },
        {
            "问题": "实现复杂度",
            "文件上传": "❌ 需要大量自定义解析代码",
            "文本上传": "✅ 利用成熟的标准库，代码简洁"
        },
        {
            "问题": "可靠性",
            "文件上传": "❌ 容易出现边界情况和错误",
            "文本上传": "✅ 经过充分测试的标准方案"
        },
        {
            "问题": "维护成本",
            "文件上传": "❌ 需要处理各种编码和格式问题",
            "文本上传": "✅ 维护成本低，问题少"
        }
    ]
    
    print("详细对比:")
    for item in advantages:
        print(f"\n{item['问题']}:")
        print(f"  文件上传: {item['文件上传']}")
        print(f"  文本上传: {item['文本上传']}")
    
    print("\n结论:")
    print("✅ 文本上传是更优的解决方案")
    print("✅ 避免了所有编码和解析问题")
    print("✅ 实现简单、可靠、易维护")
    print("✅ 完全解决HTTP负载中换行符的问题")

if __name__ == '__main__':
    test_csv_text_upload()
    test_file_vs_text_comparison()
    test_browser_encoding_handling()
    test_advantages_summary()
