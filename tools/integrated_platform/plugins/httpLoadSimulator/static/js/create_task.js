/**
 * HTTP负载模拟访问插件 - 创建任务页面脚本
 */

let currentTaskId = null;
let statusCheckInterval = null;
let selectedCsvFile = null;
let csvTextContent = null;
let csvTextData = [];
let currentInputMode = 'manual';
let csvUploadMode = 'file';

/**
 * 切换输入模式 - 全局函数，确保HTML中的onchange可以调用
 */
window.toggleInputMode = function() {
    console.log('toggleInputMode 函数被调用');
    const manualMode = document.querySelector('input[name="inputMode"][value="manual"]');
    const csvMode = document.querySelector('input[name="inputMode"][value="csv"]');
    const manualConfig = document.getElementById('manualInputConfig');
    const csvConfig = document.getElementById('csvInputConfig');

    if (manualMode && manualMode.checked) {
        currentInputMode = 'manual';
        if (manualConfig) manualConfig.style.display = 'block';
        if (csvConfig) csvConfig.style.display = 'none';

        // 重置CSV配置
        resetCsvConfig();

        // 设置手动输入模式的必填字段
        const httpPayload = document.getElementById('httpPayload');
        if (httpPayload) httpPayload.required = true;
    } else if (csvMode && csvMode.checked) {
        currentInputMode = 'csv';
        if (manualConfig) manualConfig.style.display = 'none';
        if (csvConfig) csvConfig.style.display = 'block';

        // 取消手动输入模式的必填字段
        const httpPayload = document.getElementById('httpPayload');
        if (httpPayload) httpPayload.required = false;
    }
};

/**
 * 切换CSV上传模式 - 全局函数
 */
window.toggleCsvUploadMode = function() {
    const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    const textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    const csvFileUpload = document.getElementById('csvFileUpload');
    const csvTextUpload = document.getElementById('csvTextUpload');

    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        if (csvFileUpload) csvFileUpload.style.display = 'block';
        if (csvTextUpload) csvTextUpload.style.display = 'none';

        // 重置文本上传状态
        resetCsvTextConfig();
    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        if (csvFileUpload) csvFileUpload.style.display = 'none';
        if (csvTextUpload) csvTextUpload.style.display = 'block';

        // 重置文件上传状态
        resetCsvConfig();
    }
};

/**
 * 重置CSV配置 - 全局函数
 */
window.resetCsvConfig = function() {
    selectedCsvFile = null;
    const csvFile = document.getElementById('csvFile');
    const csvFileInfo = document.getElementById('csvFileInfo');
    const uploadArea = document.getElementById('csvUploadArea');

    if (csvFile) csvFile.value = '';
    if (csvFileInfo) csvFileInfo.style.display = 'none';
    if (uploadArea) uploadArea.classList.remove('dragover');
};

/**
 * 重置CSV文本配置 - 全局函数
 */
window.resetCsvTextConfig = function() {
    csvTextContent = null;
    csvTextData = [];
    const csvTextFile = document.getElementById('csvTextFile');
    const csvTextFileInfo = document.getElementById('csvTextFileInfo');
    const csvTextPreview = document.getElementById('csvTextPreview');
    const uploadArea = document.getElementById('csvTextUploadArea');

    if (csvTextFile) csvTextFile.value = '';
    if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
    if (csvTextPreview) csvTextPreview.style.display = 'none';
    if (uploadArea) uploadArea.classList.remove('file-selected');
};

document.addEventListener('DOMContentLoaded', function() {
    console.log('HTTP负载模拟创建任务页面已加载');

    // 初始化页面
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置默认HTTP负载示例
    setDefaultHttpPayload();
    
    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 设置默认HTTP负载示例
 */
function setDefaultHttpPayload() {
    const httpPayloadTextarea = document.getElementById('httpPayload');
    if (httpPayloadTextarea && !httpPayloadTextarea.value.trim()) {
        const defaultPayload = `POST /api/test HTTP/1.1
Host: example.com
Content-Type: application/json
Content-Length: 25
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: application/json, text/plain, */*
Connection: close

{"key": "value"}`;
        
        httpPayloadTextarea.value = defaultPayload;
    }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定表单验证
    const inputs = document.querySelectorAll('.form-input, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });

    // 绑定拖拽事件
    const csvUploadArea = document.getElementById('csvUploadArea');
    if (csvUploadArea) {
        csvUploadArea.addEventListener('dragover', handleDragOver);
        csvUploadArea.addEventListener('dragleave', handleDragLeave);
        csvUploadArea.addEventListener('drop', handleDrop);
    }
}

/**
 * 处理表单提交
 */
async function handleFormSubmit(event) {
    event.preventDefault();

    // 验证表单
    if (!validateForm()) {
        return;
    }

    if (currentInputMode === 'manual') {
        // 手动输入模式
        const formData = {
            taskName: document.getElementById('taskName').value.trim(),
            httpPayload: document.getElementById('httpPayload').value.trim(),
            targetIp: document.getElementById('targetIp').value.trim(),
            targetPort: parseInt(document.getElementById('targetPort').value)
        };

        await submitTask(formData);
    } else if (currentInputMode === 'csv') {
        // CSV文件上传模式
        await submitBatchTask();
    }
}

/**
 * 验证表单
 */
function validateForm() {
    let isFormValid = true;

    // 验证基础字段（任务名称、目标IP、目标端口）
    const requiredInputs = ['taskName', 'targetIp', 'targetPort'];
    requiredInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input && !validateField(input)) {
            isFormValid = false;
        }
    });

    if (currentInputMode === 'manual') {
        // 手动输入模式验证
        const httpPayloadInput = document.getElementById('httpPayload');
        if (httpPayloadInput && !validateField(httpPayloadInput)) {
            isFormValid = false;
        }
    } else if (currentInputMode === 'csv') {
        // CSV文件上传模式验证
        if (!selectedCsvFile) {
            showAlert('请选择CSV文件', 'error');
            isFormValid = false;
        }
    }

    return isFormValid;
}

/**
 * 验证单个字段
 */
function validateField(input) {
    const value = input.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // 检查必填字段
    if (input.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    }
    
    // 特定字段验证
    switch (input.id) {
        case 'targetIp':
            if (value && !isValidIpAddress(value)) {
                isValid = false;
                errorMessage = '请输入有效的IP地址';
            }
            break;
        case 'targetPort':
            const port = parseInt(value);
            if (value && (isNaN(port) || port < 1 || port > 65535)) {
                isValid = false;
                errorMessage = '端口号必须在1-65535之间';
            }
            break;
        case 'httpPayload':
            if (value && !isValidHttpPayload(value)) {
                isValid = false;
                errorMessage = 'HTTP负载格式不正确，请检查请求行和请求头格式';
            }
            break;
    }
    
    // 显示或清除错误
    if (isValid) {
        clearFieldError(input);
    } else {
        showFieldError(input, errorMessage);
    }
    
    return isValid;
}

/**
 * 验证IP地址格式
 */
function isValidIpAddress(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

/**
 * 验证HTTP负载格式
 */
function isValidHttpPayload(payload) {
    const lines = payload.trim().split('\n');
    if (lines.length === 0) return false;
    
    // 检查请求行格式
    const requestLine = lines[0].trim();
    const requestParts = requestLine.split(' ');
    if (requestParts.length < 3) return false;
    
    // 检查HTTP方法
    const method = requestParts[0].toUpperCase();
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH'];
    if (!validMethods.includes(method)) return false;
    
    // 检查HTTP版本
    const version = requestParts[2];
    if (!version.startsWith('HTTP/')) return false;
    
    return true;
}

/**
 * 显示字段错误
 */
function showFieldError(input, message) {
    clearFieldError(input);
    
    input.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段错误
 */
function clearFieldError(input) {
    input.classList.remove('error');
    
    const errorDiv = input.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 提交任务
 */
async function submitTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建中...</span>';
        
        const response = await fetch('/api/plugins/httpLoadSimulator/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert('HTTP负载模拟任务创建成功，正在执行中...', 'success');
        } else {
            throw new Error(result.message || '创建任务失败');
        }
        
    } catch (error) {
        console.error('提交任务失败:', error);
        showAlert(`创建任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 显示任务状态面板
 */
function showTaskStatus() {
    const statusPanel = document.getElementById('statusPanel');
    const taskIdElement = document.getElementById('taskId');
    
    if (statusPanel && taskIdElement) {
        taskIdElement.textContent = currentTaskId;
        statusPanel.style.display = 'block';
        
        // 滚动到状态面板
        statusPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * 开始状态检查
 */
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkTaskStatus, 2000);
}

/**
 * 检查任务状态
 */
async function checkTaskStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/status`);
        const result = await response.json();
        
        if (result.success) {
            updateTaskStatus(result.data);
            
            // 如果任务完成，停止检查
            if (result.data.status === 'completed' || result.data.status === 'failed') {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

/**
 * 更新任务状态显示
 */
function updateTaskStatus(taskData) {
    const statusElement = document.getElementById('taskStatus');
    const resultElement = document.getElementById('taskResult');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const actionButtons = document.getElementById('actionButtons');
    
    // 更新状态显示
    if (statusElement) {
        const statusClass = `status-${taskData.status}`;
        const statusText = getStatusText(taskData.status);
        statusElement.innerHTML = `<span class="status-badge ${statusClass}">${statusText}</span>`;
    }
    
    // 更新结果显示
    if (resultElement) {
        resultElement.textContent = taskData.result || '-';
    }
    
    // 更新进度条
    if (progressFill && progressText) {
        let progress = 0;
        let progressMessage = '';
        
        switch (taskData.status) {
            case 'pending':
                progress = 10;
                progressMessage = '任务排队中...';
                break;
            case 'running':
                progress = 50;
                progressMessage = '正在执行HTTP负载模拟...';
                break;
            case 'completed':
                progress = 100;
                progressMessage = '任务执行完成';
                break;
            case 'failed':
                progress = 100;
                progressMessage = '任务执行失败';
                break;
        }
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = progressMessage;
    }
    
    // 显示操作按钮
    if (actionButtons && (taskData.status === 'completed' || taskData.status === 'failed')) {
        actionButtons.style.display = 'flex';

        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.style.display = taskData.hasReport ? 'inline-flex' : 'none';
        }

        // 任务完成后3秒自动跳转到任务结果页面
        if (taskData.status === 'completed') {
            setTimeout(() => {
                showAlert('任务执行完成，即将跳转到任务结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = '/plugins/httpLoadSimulator/task-results';
                }, 1500);
            }, 3000);
        }
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };

    return statusMap[status] || status;
}

/**
 * 下载结果文件
 */
function downloadResult() {
    if (currentTaskId) {
        window.open(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/download-report`, '_blank');
    }
}

/**
 * 查看结果
 */
function viewResults() {
    window.location.href = '/plugins/httpLoadSimulator/task-results';
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('createTaskForm');
    if (form) {
        form.reset();

        // 清除所有错误状态
        const inputs = form.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
            clearFieldError(input);
        });

        // 重新设置默认HTTP负载
        setDefaultHttpPayload();

        // 隐藏状态面板
        const statusPanel = document.getElementById('statusPanel');
        if (statusPanel) {
            statusPanel.style.display = 'none';
        }

        // 清除状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }

        currentTaskId = null;
    }
}

/**
 * 显示提示框
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;

    const iconMap = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    };

    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="alert-content">
            <div class="alert-message">${message}</div>
        </div>
    `;

    alertContainer.appendChild(alertDiv);

    // 显示动画
    setTimeout(() => {
        alertDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 4000);
}

// 添加CSS样式用于字段错误显示
const style = document.createElement('style');
style.textContent = `
    .form-input.error,
    .form-textarea.error {
        border-color: #f56565 !important;
        box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;
    }

    .field-error {
        color: #f56565;
        font-size: 0.85rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .field-error::before {
        content: "⚠";
        font-size: 0.9rem;
    }
`;
document.head.appendChild(style);

/**
 * 提交批量任务
 */
async function submitBatchTask() {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;

    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建批量任务中...</span>';

        let response;

        if (csvUploadMode === 'text') {
            // 文本上传模式
            if (!csvTextContent) {
                showAlert('请先选择CSV文件', 'error');
                return;
            }

            const requestData = {
                csvText: csvTextContent,
                fileName: document.getElementById('csvTextFileName').textContent,
                taskName: document.getElementById('taskName').value.trim(),
                targetIp: document.getElementById('targetIp').value.trim(),
                targetPort: parseInt(document.getElementById('targetPort').value)
            };

            response = await fetch('/api/plugins/httpLoadSimulator/upload-csv-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

        } else {
            // 文件上传模式
            if (!selectedCsvFile) {
                showAlert('请先选择CSV文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('taskName', document.getElementById('taskName').value.trim());
            formData.append('targetIp', document.getElementById('targetIp').value.trim());
            formData.append('targetPort', document.getElementById('targetPort').value);
            formData.append('csvFile', selectedCsvFile);

            response = await fetch('/api/plugins/httpLoadSimulator/batch-tasks', {
                method: 'POST',
                body: formData
            });
        }

        const result = await response.json();

        if (result.success) {
            const mode = csvUploadMode === 'text' ? '文本' : '文件';
            const taskId = result.taskId || result.data?.taskId;
            currentTaskId = taskId;

            if (csvUploadMode === 'text') {
                showAlert(`CSV文本上传成功！解析了 ${result.count} 个测试项目`, 'success');
            } else {
                showTaskStatus();
                startStatusCheck();
                showAlert(`批量HTTP负载模拟任务创建成功，正在执行中...`, 'success');
            }
        } else {
            throw new Error(result.message || '创建批量任务失败');
        }

    } catch (error) {
        console.error('提交批量任务失败:', error);
        showAlert(`创建批量任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 处理CSV文件选择
 */
function handleCsvFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        selectedCsvFile = file;
        showCsvFileInfo(file);
    }
}

/**
 * 处理CSV文本文件选择
 */
function handleCsvTextFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showMessage('请选择CSV格式的文件', 'error');
        return;
    }

    // 验证文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        showMessage('文件大小不能超过10MB', 'error');
        return;
    }

    // 使用FileReader以文本方式读取文件
    const reader = new FileReader();

    reader.onload = function(e) {
        csvTextContent = e.target.result;
        processCsvText(file);
    };

    reader.onerror = function() {
        showMessage('文件读取失败，请重试', 'error');
    };

    // 以文本方式读取，让浏览器自动处理编码
    reader.readAsText(file, 'UTF-8');

    console.log('开始读取CSV文本文件:', file.name);
}

/**
 * 处理CSV文本内容
 */
function processCsvText(file) {
    try {
        // 解析CSV文本
        csvTextData = parseCsvText(csvTextContent);

        // 显示文件信息
        showCsvTextFileInfo(file, csvTextData);

        // 显示预览
        showCsvTextPreview(csvTextData);

        // 标记上传区域
        const uploadArea = document.getElementById('csvTextUploadArea');
        if (uploadArea) uploadArea.classList.add('file-selected');

        console.log('CSV文本解析完成，共', csvTextData.length, '个测试项目');

    } catch (error) {
        console.error('CSV文本解析失败:', error);
        showMessage('CSV文件解析失败: ' + error.message, 'error');
    }
}

/**
 * 解析CSV文本内容
 */
function parseCsvText(text) {
    const lines = text.split('\n');
    const result = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            const row = parseCsvLine(line);
            result.push({
                rowIndex: i + 1,
                data: row,
                rawLine: line
            });
        }
    }

    return result;
}

/**
 * 解析CSV行
 */
function parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // 转义的引号
                current += '"';
                i++; // 跳过下一个引号
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            // 字段分隔符
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    // 添加最后一个字段
    result.push(current.trim());

    return result;
}

/**
 * 显示CSV文本文件信息
 */
function showCsvTextFileInfo(file, csvData) {
    const csvTextFileName = document.getElementById('csvTextFileName');
    const csvTextFileSize = document.getElementById('csvTextFileSize');
    const csvTextLineCount = document.getElementById('csvTextLineCount');
    const csvTextProjectCount = document.getElementById('csvTextProjectCount');
    const csvTextFileInfo = document.getElementById('csvTextFileInfo');

    if (csvTextFileName) csvTextFileName.textContent = file.name;
    if (csvTextFileSize) csvTextFileSize.textContent = formatFileSize(file.size);
    if (csvTextLineCount) csvTextLineCount.textContent = csvData.length;

    // 计算有效的测试项目数（排除标题行）
    const projectCount = Math.max(0, csvData.length - 1);
    if (csvTextProjectCount) csvTextProjectCount.textContent = projectCount;

    if (csvTextFileInfo) csvTextFileInfo.style.display = 'block';
}

/**
 * 显示CSV文本预览
 */
function showCsvTextPreview(csvData) {
    const csvPreviewContent = document.getElementById('csvPreviewContent');
    const csvTextPreview = document.getElementById('csvTextPreview');

    if (!csvPreviewContent || !csvTextPreview) return;

    const previewLines = csvData.slice(0, 5); // 只显示前5行
    let previewText = '';

    previewLines.forEach((row, index) => {
        previewText += `行 ${row.rowIndex}: ${row.rawLine}\n`;

        // 如果是数据行，显示解析后的字段
        if (index > 0 && row.data.length >= 2) {
            previewText += `  -> 测试名称: ${row.data[0]}\n`;
            const payload = row.data[1];
            const payloadPreview = payload.length > 80 ? payload.substring(0, 80) + '...' : payload;
            previewText += `  -> HTTP负载: ${payloadPreview}\n`;
        }
        previewText += '\n';
    });

    csvPreviewContent.textContent = previewText;
    csvTextPreview.style.display = 'block';
}

/**
 * 显示CSV文件信息
 */
function showCsvFileInfo(file) {
    const csvFileInfo = document.getElementById('csvFileInfo');
    const fileName = document.getElementById('csvFileName');
    const fileSize = document.getElementById('csvFileSize');

    if (csvFileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        csvFileInfo.style.display = 'block';
    }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 处理拖拽上传
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

/**
 * 处理文件拖拽放置
 */
function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
            selectedCsvFile = file;
            showCsvFileInfo(file);

            // 更新文件输入框
            const csvFileInput = document.getElementById('csvFile');
            if (csvFileInput) {
                // 注意：由于安全限制，我们不能直接设置文件输入框的值
                // 但我们可以显示文件信息
            }
        } else {
            showAlert('请选择CSV格式的文件', 'error');
        }
    }
}

// 全局函数声明
window.handleCsvFileSelect = handleCsvFileSelect;
window.handleCsvTextFileSelect = handleCsvTextFileSelect;
window.toggleCsvUploadMode = function() {
    const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    const textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    const csvFileUpload = document.getElementById('csvFileUpload');
    const csvTextUpload = document.getElementById('csvTextUpload');

    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        if (csvFileUpload) csvFileUpload.style.display = 'block';
        if (csvTextUpload) csvTextUpload.style.display = 'none';

        // 重置文本上传状态
        csvTextContent = null;
        csvTextData = [];
        const csvTextFileInfo = document.getElementById('csvTextFileInfo');
        const csvTextPreview = document.getElementById('csvTextPreview');
        if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
        if (csvTextPreview) csvTextPreview.style.display = 'none';

    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        if (csvFileUpload) csvFileUpload.style.display = 'none';
        if (csvTextUpload) csvTextUpload.style.display = 'block';

        // 重置文件上传状态
        selectedCsvFile = null;
        const csvFileInfo = document.getElementById('csvFileInfo');
        if (csvFileInfo) csvFileInfo.style.display = 'none';
    }
};
