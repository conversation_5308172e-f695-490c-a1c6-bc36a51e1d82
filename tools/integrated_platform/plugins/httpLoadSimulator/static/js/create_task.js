/**
 * HTTP负载模拟访问插件 - 创建任务页面脚本
 */

// 全局变量声明
var currentTaskId = null;
var statusCheckInterval = null;
var selectedCsvFile = null;
var csvTextContent = null;
var csvTextData = [];
var currentInputMode = 'manual';
var csvUploadMode = 'file';

// 立即定义全局函数，确保HTML中的onchange可以调用
function toggleInputMode() {
    console.log('toggleInputMode 函数被调用');
    var manualMode = document.querySelector('input[name="inputMode"][value="manual"]');
    var csvMode = document.querySelector('input[name="inputMode"][value="csv"]');
    var manualConfig = document.getElementById('manualInputConfig');
    var csvConfig = document.getElementById('csvInputConfig');

    if (manualMode && manualMode.checked) {
        currentInputMode = 'manual';
        if (manualConfig) manualConfig.style.display = 'block';
        if (csvConfig) csvConfig.style.display = 'none';

        // 重置CSV配置
        if (typeof resetCsvConfig === 'function') {
            resetCsvConfig();
        }

        // 设置手动输入模式的必填字段
        var httpPayload = document.getElementById('httpPayload');
        if (httpPayload) httpPayload.required = true;
    } else if (csvMode && csvMode.checked) {
        currentInputMode = 'csv';
        if (manualConfig) manualConfig.style.display = 'none';
        if (csvConfig) csvConfig.style.display = 'block';

        // 取消手动输入模式的必填字段
        var httpPayload = document.getElementById('httpPayload');
        if (httpPayload) httpPayload.required = false;
    }
}

function toggleCsvUploadMode() {
    var fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    var textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    var csvFileUpload = document.getElementById('csvFileUpload');
    var csvTextUpload = document.getElementById('csvTextUpload');

    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        if (csvFileUpload) csvFileUpload.style.display = 'block';
        if (csvTextUpload) csvTextUpload.style.display = 'none';

        // 重置文本上传状态
        csvTextContent = null;
        csvTextData = [];
        var csvTextFileInfo = document.getElementById('csvTextFileInfo');
        var csvTextPreview = document.getElementById('csvTextPreview');
        if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
        if (csvTextPreview) csvTextPreview.style.display = 'none';

    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        if (csvFileUpload) csvFileUpload.style.display = 'none';
        if (csvTextUpload) csvTextUpload.style.display = 'block';

        // 重置文件上传状态
        selectedCsvFile = null;
        var csvFileInfo = document.getElementById('csvFileInfo');
        if (csvFileInfo) csvFileInfo.style.display = 'none';
    }
}

// 立即暴露到全局作用域
window.toggleInputMode = toggleInputMode;
window.toggleCsvUploadMode = toggleCsvUploadMode;

function resetCsvConfig() {
    selectedCsvFile = null;
    var csvFile = document.getElementById('csvFile');
    var csvFileInfo = document.getElementById('csvFileInfo');
    var uploadArea = document.getElementById('csvUploadArea');

    if (csvFile) csvFile.value = '';
    if (csvFileInfo) csvFileInfo.style.display = 'none';
    if (uploadArea) uploadArea.classList.remove('file-selected');
}

function resetCsvTextConfig() {
    csvTextContent = null;
    csvTextData = [];
    var csvTextFile = document.getElementById('csvTextFile');
    var csvTextFileInfo = document.getElementById('csvTextFileInfo');
    var csvTextPreview = document.getElementById('csvTextPreview');
    var uploadArea = document.getElementById('csvTextUploadArea');

    if (csvTextFile) csvTextFile.value = '';
    if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
    if (csvTextPreview) csvTextPreview.style.display = 'none';
    if (uploadArea) uploadArea.classList.remove('file-selected');
}

function handleCsvFileSelect(event) {
    var file = event.target.files[0];
    if (file) {
        selectedCsvFile = file;
        showCsvFileInfo(file);
    }
}

function handleCsvTextFileSelect(event) {
    var file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showAlert('请选择CSV格式的文件', 'error');
        return;
    }

    // 验证文件大小（10MB限制）
    var maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        showAlert('文件大小不能超过10MB', 'error');
        return;
    }

    // 使用智能编码检测读取文件
    detectFileEncodingAndRead(file, function(content, encoding) {
        if (content) {
            csvTextContent = content;
            console.log('文件读取成功，使用编码:', encoding);
            processCsvText(file);
        } else {
            showAlert('文件读取失败，请检查文件编码', 'error');
        }
    });

    console.log('开始读取CSV文本文件:', file.name);
}

/**
 * 智能检测文件编码并读取
 */
function detectFileEncodingAndRead(file, callback) {
    // 首先读取文件的前几KB来检测编码
    var reader = new FileReader();

    reader.onload = function(e) {
        var arrayBuffer = e.target.result;
        var bytes = new Uint8Array(arrayBuffer);

        // 检测编码
        var detectedEncoding = detectEncodingFromBytes(bytes);
        console.log('检测到的编码:', detectedEncoding);

        // 使用检测到的编码读取整个文件
        readFileWithEncoding(file, detectedEncoding, callback);
    };

    reader.onerror = function() {
        console.error('文件读取失败');
        callback(null, null);
    };

    // 读取文件的前8KB用于编码检测
    var blob = file.slice(0, 8192);
    reader.readAsArrayBuffer(blob);
}

/**
 * 从字节数组检测编码
 */
function detectEncodingFromBytes(bytes) {
    // 检查BOM
    if (bytes.length >= 3 && bytes[0] === 0xEF && bytes[1] === 0xBB && bytes[2] === 0xBF) {
        return 'UTF-8'; // UTF-8 BOM
    }

    if (bytes.length >= 2) {
        if (bytes[0] === 0xFF && bytes[1] === 0xFE) {
            return 'UTF-16LE'; // UTF-16 LE BOM
        }
        if (bytes[0] === 0xFE && bytes[1] === 0xFF) {
            return 'UTF-16BE'; // UTF-16 BE BOM
        }
    }

    // 简单的中文编码检测
    var hasHighBytes = false;
    var utf8LikeCount = 0;
    var gbkLikeCount = 0;

    for (var i = 0; i < Math.min(bytes.length, 1000); i++) {
        if (bytes[i] > 127) {
            hasHighBytes = true;

            // 检测UTF-8模式
            if (i + 2 < bytes.length) {
                if ((bytes[i] & 0xE0) === 0xE0 && (bytes[i+1] & 0xC0) === 0x80 && (bytes[i+2] & 0xC0) === 0x80) {
                    utf8LikeCount++;
                }
            }

            // 检测GBK模式 (简化检测)
            if (i + 1 < bytes.length) {
                if (bytes[i] >= 0xA1 && bytes[i] <= 0xFE && bytes[i+1] >= 0xA1 && bytes[i+1] <= 0xFE) {
                    gbkLikeCount++;
                }
            }
        }
    }

    // 如果没有高字节，可能是纯ASCII，使用UTF-8
    if (!hasHighBytes) {
        return 'UTF-8';
    }

    // 根据检测结果选择编码
    if (utf8LikeCount > gbkLikeCount) {
        return 'UTF-8';
    } else if (gbkLikeCount > 0) {
        return 'GBK';
    } else {
        return 'UTF-8'; // 默认
    }
}

/**
 * 使用指定编码读取文件
 */
function readFileWithEncoding(file, encoding, callback) {
    var encodingsToTry = [encoding];

    // 添加备选编码
    if (encoding !== 'UTF-8') encodingsToTry.push('UTF-8');
    if (encoding !== 'GBK') encodingsToTry.push('GBK');
    if (encoding !== 'GB2312') encodingsToTry.push('GB2312');

    tryReadWithEncodings(file, encodingsToTry, 0, callback);
}

/**
 * 尝试使用不同编码读取文件
 */
function tryReadWithEncodings(file, encodings, index, callback) {
    if (index >= encodings.length) {
        console.error('所有编码都尝试失败');
        callback(null, null);
        return;
    }

    var encoding = encodings[index];
    var reader = new FileReader();

    reader.onload = function(e) {
        var content = e.target.result;

        // 验证内容是否合理
        if (isValidTextContent(content)) {
            console.log('成功使用编码读取:', encoding);
            callback(content, encoding);
        } else {
            console.log('编码', encoding, '读取结果不合理，尝试下一个');
            tryReadWithEncodings(file, encodings, index + 1, callback);
        }
    };

    reader.onerror = function() {
        console.log('编码', encoding, '读取失败，尝试下一个');
        tryReadWithEncodings(file, encodings, index + 1, callback);
    };

    try {
        reader.readAsText(file, encoding);
    } catch (error) {
        console.log('编码', encoding, '不支持，尝试下一个');
        tryReadWithEncodings(file, encodings, index + 1, callback);
    }
}

/**
 * 验证文本内容是否合理
 */
function isValidTextContent(text) {
    if (!text || text.length === 0) {
        return false;
    }

    // 检查是否包含过多的替换字符或控制字符
    var replacementCharCount = (text.match(/\uFFFD/g) || []).length;
    var controlCharCount = (text.match(/[\x00-\x08\x0B\x0C\x0E-\x1F]/g) || []).length;

    // 如果替换字符或控制字符过多，认为编码不正确
    if (text.length > 0) {
        var replacementRatio = replacementCharCount / text.length;
        var controlRatio = controlCharCount / text.length;

        if (replacementRatio > 0.1 || controlRatio > 0.1) {
            return false;
        }
    }

    // 检查是否包含合理的CSV结构
    var lines = text.split('\n');
    if (lines.length < 2) {
        return false;
    }

    // 检查第一行是否像标题行
    var firstLine = lines[0].trim();
    if (firstLine.length === 0) {
        return false;
    }

    return true;
}

function processCsvText(file) {
    try {
        // 解析CSV文本
        csvTextData = parseCsvText(csvTextContent);

        // 显示文件信息
        showCsvTextFileInfo(file, csvTextData);

        // 显示预览
        showCsvTextPreview(csvTextData);

        // 标记上传区域
        var uploadArea = document.getElementById('csvTextUploadArea');
        if (uploadArea) uploadArea.classList.add('file-selected');

        console.log('CSV文本解析完成，共', csvTextData.length, '个测试项目');

    } catch (error) {
        console.error('CSV文本解析失败:', error);
        showAlert('CSV文件解析失败: ' + error.message, 'error');
    }
}

function downloadCsvTemplate() {
    // 创建CSV模板内容
    var csvTemplate = '测试项目名称,HTTP负载\n' +
        '"示例GET请求","GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: Mozilla/5.0\\r\\nAccept: application/json\\r\\n\\r\\n"\n' +
        '"示例POST请求","POST /api/data HTTP/1.1\\r\\nHost: example.com\\r\\nContent-Type: application/json\\r\\nContent-Length: 25\\r\\n\\r\\n{""key"": ""value""}"';

    // 创建Blob对象
    var blob = new Blob([csvTemplate], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    var link = document.createElement('a');
    var url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'http_load_template.csv');
    link.style.visibility = 'hidden';

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function resetForm() {
    var form = document.getElementById('createTaskForm');
    if (form) {
        form.reset();

        // 清除所有错误状态
        var inputs = form.querySelectorAll('.form-input, .form-textarea');
        for (var i = 0; i < inputs.length; i++) {
            clearFieldError(inputs[i]);
        }

        // 重置到手动输入模式
        currentInputMode = 'manual';
        var manualMode = document.querySelector('input[name="inputMode"][value="manual"]');
        if (manualMode) manualMode.checked = true;
        toggleInputMode();

        // 重置任务状态面板
        hideTaskStatus();

        // 清除任务ID
        currentTaskId = null;

        // 停止状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
    }
}

function downloadResult() {
    if (currentTaskId) {
        window.open('/api/plugins/httpLoadSimulator/tasks/' + currentTaskId + '/download-report', '_blank');
    }
}

function viewResults() {
    window.location.href = '/plugins/httpLoadSimulator/task-results';
}

// 立即暴露到全局作用域
window.resetCsvConfig = resetCsvConfig;
window.resetCsvTextConfig = resetCsvTextConfig;
window.handleCsvFileSelect = handleCsvFileSelect;
window.handleCsvTextFileSelect = handleCsvTextFileSelect;
window.processCsvText = processCsvText;
window.detectFileEncodingAndRead = detectFileEncodingAndRead;
window.detectEncodingFromBytes = detectEncodingFromBytes;
window.downloadCsvTemplate = downloadCsvTemplate;
window.resetForm = resetForm;
window.downloadResult = downloadResult;
window.viewResults = viewResults;

// 注意：resetCsvConfig 和 resetCsvTextConfig 函数已在前面定义

// 兼容旧版本浏览器的DOM加载检测
function initWhenReady() {
    if (document.readyState === 'loading') {
        // 如果文档还在加载中，等待DOMContentLoaded事件
        if (document.addEventListener) {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('HTTP负载模拟创建任务页面已加载');
                initializePage();
            });
        } else if (document.attachEvent) {
            // IE8及以下版本
            document.attachEvent('onreadystatechange', function() {
                if (document.readyState === 'complete') {
                    console.log('HTTP负载模拟创建任务页面已加载');
                    initializePage();
                }
            });
        }
    } else {
        // 文档已经加载完成，直接初始化
        console.log('HTTP负载模拟创建任务页面已加载');
        initializePage();
    }
}

// 立即执行初始化检测
initWhenReady();

/**
 * 初始化页面
 */
function initializePage() {
    // 设置默认HTTP负载示例
    setDefaultHttpPayload();
    
    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 设置默认HTTP负载示例
 */
function setDefaultHttpPayload() {
    var httpPayloadTextarea = document.getElementById('httpPayload');
    if (httpPayloadTextarea && !httpPayloadTextarea.value.trim()) {
        var defaultPayload = 'POST /api/test HTTP/1.1\n' +
            'Host: example.com\n' +
            'Content-Type: application/json\n' +
            'Content-Length: 25\n' +
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\n' +
            'Accept: application/json, text/plain, */*\n' +
            'Connection: close\n' +
            '\n' +
            '{"key": "value"}';

        httpPayloadTextarea.value = defaultPayload;
    }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定表单验证
    const inputs = document.querySelectorAll('.form-input, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });

    // 绑定拖拽事件
    const csvUploadArea = document.getElementById('csvUploadArea');
    if (csvUploadArea) {
        csvUploadArea.addEventListener('dragover', handleDragOver);
        csvUploadArea.addEventListener('dragleave', handleDragLeave);
        csvUploadArea.addEventListener('drop', handleDrop);
    }
}

/**
 * 处理表单提交
 */
async function handleFormSubmit(event) {
    event.preventDefault();

    // 验证表单
    if (!validateForm()) {
        return;
    }

    if (currentInputMode === 'manual') {
        // 手动输入模式
        const formData = {
            taskName: document.getElementById('taskName').value.trim(),
            httpPayload: document.getElementById('httpPayload').value.trim(),
            targetIp: document.getElementById('targetIp').value.trim(),
            targetPort: parseInt(document.getElementById('targetPort').value)
        };

        submitTask(formData);
    } else if (currentInputMode === 'csv') {
        // CSV文件上传模式
        submitBatchTask();
    }
}

/**
 * 验证表单
 */
function validateForm() {
    let isFormValid = true;

    // 验证基础字段（任务名称、目标IP、目标端口）
    const requiredInputs = ['taskName', 'targetIp', 'targetPort'];
    requiredInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input && !validateField(input)) {
            isFormValid = false;
        }
    });

    if (currentInputMode === 'manual') {
        // 手动输入模式验证
        const httpPayloadInput = document.getElementById('httpPayload');
        if (httpPayloadInput && !validateField(httpPayloadInput)) {
            isFormValid = false;
        }
    } else if (currentInputMode === 'csv') {
        // CSV上传模式验证 - 根据上传方式检查不同的变量
        if (csvUploadMode === 'text') {
            // 文本上传方式
            if (!csvTextContent) {
                showAlert('请选择CSV文件进行文本上传', 'error');
                isFormValid = false;
            }
        } else {
            // 文件上传方式
            if (!selectedCsvFile) {
                showAlert('请选择CSV文件进行文件上传', 'error');
                isFormValid = false;
            }
        }
    }

    return isFormValid;
}

/**
 * 验证单个字段
 */
function validateField(input) {
    const value = input.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // 检查必填字段
    if (input.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    }
    
    // 特定字段验证
    switch (input.id) {
        case 'targetIp':
            if (value && !isValidIpAddress(value)) {
                isValid = false;
                errorMessage = '请输入有效的IP地址';
            }
            break;
        case 'targetPort':
            const port = parseInt(value);
            if (value && (isNaN(port) || port < 1 || port > 65535)) {
                isValid = false;
                errorMessage = '端口号必须在1-65535之间';
            }
            break;
        case 'httpPayload':
            if (value && !isValidHttpPayload(value)) {
                isValid = false;
                errorMessage = 'HTTP负载格式不正确，请检查请求行和请求头格式';
            }
            break;
    }
    
    // 显示或清除错误
    if (isValid) {
        clearFieldError(input);
    } else {
        showFieldError(input, errorMessage);
    }
    
    return isValid;
}

/**
 * 验证IP地址格式
 */
function isValidIpAddress(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

/**
 * 验证HTTP负载格式
 */
function isValidHttpPayload(payload) {
    const lines = payload.trim().split('\n');
    if (lines.length === 0) return false;
    
    // 检查请求行格式
    const requestLine = lines[0].trim();
    const requestParts = requestLine.split(' ');
    if (requestParts.length < 3) return false;
    
    // 检查HTTP方法
    const method = requestParts[0].toUpperCase();
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH'];
    if (!validMethods.includes(method)) return false;
    
    // 检查HTTP版本
    const version = requestParts[2];
    if (!version.startsWith('HTTP/')) return false;
    
    return true;
}

/**
 * 显示字段错误
 */
function showFieldError(input, message) {
    clearFieldError(input);
    
    input.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段错误
 */
function clearFieldError(input) {
    input.classList.remove('error');
    
    const errorDiv = input.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 提交任务
 */
async function submitTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建中...</span>';
        
        const response = await fetch('/api/plugins/httpLoadSimulator/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert('HTTP负载模拟任务创建成功，正在执行中...', 'success');
        } else {
            throw new Error(result.message || '创建任务失败');
        }
        
    } catch (error) {
        console.error('提交任务失败:', error);
        showAlert('创建任务失败: ' + error.message, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 显示任务状态面板
 */
function showTaskStatus() {
    const statusPanel = document.getElementById('statusPanel');
    const taskIdElement = document.getElementById('taskId');
    
    if (statusPanel && taskIdElement) {
        taskIdElement.textContent = currentTaskId;
        statusPanel.style.display = 'block';
        
        // 滚动到状态面板
        statusPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * 开始状态检查
 */
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkTaskStatus, 2000);
}

/**
 * 检查任务状态
 */
async function checkTaskStatus() {
    if (!currentTaskId) return;
    
    try {
        var response = await fetch('/api/plugins/httpLoadSimulator/tasks/' + currentTaskId + '/status');
        const result = await response.json();
        
        if (result.success) {
            updateTaskStatus(result.data);
            
            // 如果任务完成，停止检查
            if (result.data.status === 'completed' || result.data.status === 'failed') {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

/**
 * 更新任务状态显示
 */
function updateTaskStatus(taskData) {
    var statusElement = document.getElementById('taskStatus');
    var resultElement = document.getElementById('taskResult');
    var progressFill = document.getElementById('progressFill');
    var progressText = document.getElementById('progressText');
    var actionButtons = document.getElementById('actionButtons');

    // 更新状态显示
    if (statusElement) {
        var statusClass = 'status-' + taskData.status;
        var statusText = getStatusText(taskData.status);
        statusElement.innerHTML = '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
    }
    
    // 更新结果显示
    if (resultElement) {
        resultElement.textContent = taskData.result || '-';
    }
    
    // 更新进度条
    if (progressFill && progressText) {
        let progress = 0;
        let progressMessage = '';
        
        switch (taskData.status) {
            case 'pending':
                progress = 10;
                progressMessage = '任务排队中...';
                break;
            case 'running':
                progress = 50;
                progressMessage = '正在执行HTTP负载模拟...';
                break;
            case 'completed':
                progress = 100;
                progressMessage = '任务执行完成';
                break;
            case 'failed':
                progress = 100;
                progressMessage = '任务执行失败';
                break;
        }
        
        progressFill.style.width = progress + '%';
        progressText.textContent = progressMessage;
    }
    
    // 显示操作按钮
    if (actionButtons && (taskData.status === 'completed' || taskData.status === 'failed')) {
        actionButtons.style.display = 'flex';

        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.style.display = taskData.hasReport ? 'inline-flex' : 'none';
        }

        // 任务完成后3秒自动跳转到任务结果页面
        if (taskData.status === 'completed') {
            setTimeout(() => {
                showAlert('任务执行完成，即将跳转到任务结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = '/plugins/httpLoadSimulator/task-results';
                }, 1500);
            }, 3000);
        }
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };

    return statusMap[status] || status;
}

/**
 * 下载结果文件
 */
function downloadResult() {
    if (currentTaskId) {
        window.open(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/download-report`, '_blank');
    }
}

/**
 * 查看结果
 */
function viewResults() {
    window.location.href = '/plugins/httpLoadSimulator/task-results';
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('createTaskForm');
    if (form) {
        form.reset();

        // 清除所有错误状态
        const inputs = form.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
            clearFieldError(input);
        });

        // 重新设置默认HTTP负载
        setDefaultHttpPayload();

        // 隐藏状态面板
        const statusPanel = document.getElementById('statusPanel');
        if (statusPanel) {
            statusPanel.style.display = 'none';
        }

        // 清除状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }

        currentTaskId = null;
    }
}

/**
 * 显示提示框
 */
function showAlert(message, type) {
    if (typeof type === 'undefined') type = 'info';

    var alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    var alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-' + type;

    var iconMap = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    };

    alertDiv.innerHTML =
        '<div class="alert-icon">' +
            '<i class="' + (iconMap[type] || iconMap.info) + '"></i>' +
        '</div>' +
        '<div class="alert-content">' +
            '<div class="alert-message">' + message + '</div>' +
        '</div>';

    alertContainer.appendChild(alertDiv);

    // 显示动画
    setTimeout(() => {
        alertDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 4000);
}

// 添加CSS样式用于字段错误显示
var style = document.createElement('style');
style.textContent =
    '.form-input.error,' +
    '.form-textarea.error {' +
        'border-color: #f56565 !important;' +
        'box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;' +
    '}' +
    '.field-error {' +
        'color: #f56565;' +
        'font-size: 0.85rem;' +
        'margin-top: 5px;' +
        'display: flex;' +
        'align-items: center;' +
        'gap: 5px;' +
    '}' +
    '.field-error::before {' +
        'content: "⚠";' +
        'font-size: 0.9rem;' +
    '}';
document.head.appendChild(style);

/**
 * 提交批量任务
 */
function submitBatchTask() {
    var submitBtn = document.getElementById('submitBtn');
    var originalText = submitBtn.innerHTML;

    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建批量任务中...</span>';

    if (csvUploadMode === 'text') {
        // 文本上传模式
        if (!csvTextContent) {
            showAlert('请先选择CSV文件', 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        var requestData = {
            csvText: csvTextContent,
            fileName: document.getElementById('csvTextFileName').textContent,
            taskName: document.getElementById('taskName').value.trim(),
            targetIp: document.getElementById('targetIp').value.trim(),
            targetPort: parseInt(document.getElementById('targetPort').value)
        };

        fetch('/api/plugins/httpLoadSimulator/upload-csv-text', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(result) {
            if (result.success) {
                var taskId = result.taskId || (result.data && result.data.taskId);
                currentTaskId = taskId;
                showAlert('CSV文本上传成功！解析了 ' + result.count + ' 个测试项目', 'success');
            } else {
                throw new Error(result.message || '创建批量任务失败');
            }
        })
        .catch(function(error) {
            console.error('提交批量任务失败:', error);
            showAlert('创建批量任务失败: ' + error.message, 'error');
        })
        .finally(function() {
            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });

    } else {
        // 文件上传模式
        if (!selectedCsvFile) {
            showAlert('请先选择CSV文件', 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        var formData = new FormData();
        formData.append('taskName', document.getElementById('taskName').value.trim());
        formData.append('targetIp', document.getElementById('targetIp').value.trim());
        formData.append('targetPort', document.getElementById('targetPort').value);
        formData.append('csvFile', selectedCsvFile);

        fetch('/api/plugins/httpLoadSimulator/batch-tasks', {
            method: 'POST',
            body: formData
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(result) {
            if (result.success) {
                var taskId = result.taskId || (result.data && result.data.taskId);
                currentTaskId = taskId;
                showTaskStatus();
                startStatusCheck();
                showAlert('批量HTTP负载模拟任务创建成功，正在执行中...', 'success');
            } else {
                throw new Error(result.message || '创建批量任务失败');
            }
        })
        .catch(function(error) {
            console.error('提交批量任务失败:', error);
            showAlert('创建批量任务失败: ' + error.message, 'error');
        })
        .finally(function() {
            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    }
}

// 注意：handleCsvFileSelect 和 handleCsvTextFileSelect 函数已在前面定义

/**
 * 解析CSV文本内容 - 正确处理引号内的换行符
 */
function parseCsvText(text) {
    var result = [];
    var currentLine = '';
    var inQuotes = false;
    var rowIndex = 0;

    // 逐字符解析，正确处理引号内的换行符
    for (var i = 0; i < text.length; i++) {
        var char = text[i];
        var nextChar = i + 1 < text.length ? text[i + 1] : '';

        if (char === '"') {
            if (inQuotes && nextChar === '"') {
                // 转义的引号 ""
                currentLine += '""';
                i++; // 跳过下一个引号
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
                currentLine += char;
            }
        } else if ((char === '\n' || char === '\r') && !inQuotes) {
            // 只有在引号外的换行符才是真正的行分隔符
            if (currentLine.trim()) {
                rowIndex++;
                var row = parseCsvLine(currentLine.trim());
                result.push({
                    rowIndex: rowIndex,
                    data: row,
                    rawLine: currentLine.trim()
                });
                currentLine = '';
            }
            // 跳过 \r\n 中的 \n
            if (char === '\r' && nextChar === '\n') {
                i++;
            }
        } else if (char !== '\r' || inQuotes) {
            // 添加字符到当前行（引号外的\r被忽略，引号内的\r保留）
            currentLine += char;
        }
    }

    // 处理最后一行
    if (currentLine.trim()) {
        rowIndex++;
        var row = parseCsvLine(currentLine.trim());
        result.push({
            rowIndex: rowIndex,
            data: row,
            rawLine: currentLine.trim()
        });
    }

    return result;
}

/**
 * 解析CSV行
 */
function parseCsvLine(line) {
    var result = [];
    var current = '';
    var inQuotes = false;

    for (var i = 0; i < line.length; i++) {
        var char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // 转义的引号
                current += '"';
                i++; // 跳过下一个引号
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            // 字段分隔符
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    // 添加最后一个字段
    result.push(current.trim());

    return result;
}

/**
 * 显示CSV文本文件信息
 */
function showCsvTextFileInfo(file, csvData) {
    var csvTextFileName = document.getElementById('csvTextFileName');
    var csvTextFileSize = document.getElementById('csvTextFileSize');
    var csvTextLineCount = document.getElementById('csvTextLineCount');
    var csvTextProjectCount = document.getElementById('csvTextProjectCount');
    var csvTextFileInfo = document.getElementById('csvTextFileInfo');

    if (csvTextFileName) csvTextFileName.textContent = file.name;
    if (csvTextFileSize) csvTextFileSize.textContent = formatFileSize(file.size);
    if (csvTextLineCount) csvTextLineCount.textContent = csvData.length;

    // 计算有效的测试项目数（排除标题行）
    var projectCount = Math.max(0, csvData.length - 1);
    if (csvTextProjectCount) csvTextProjectCount.textContent = projectCount;

    if (csvTextFileInfo) csvTextFileInfo.style.display = 'block';
}

/**
 * 显示CSV文本预览
 */
function showCsvTextPreview(csvData) {
    const csvPreviewContent = document.getElementById('csvPreviewContent');
    const csvTextPreview = document.getElementById('csvTextPreview');

    if (!csvPreviewContent || !csvTextPreview) return;

    var previewLines = csvData.slice(0, 5); // 只显示前5行
    var previewText = '';

    for (var i = 0; i < previewLines.length; i++) {
        var row = previewLines[i];
        previewText += '行 ' + row.rowIndex + ': ' + row.rawLine + '\n';

        // 如果是数据行，显示解析后的字段
        if (i > 0 && row.data.length >= 2) {
            previewText += '  -> 测试名称: ' + row.data[0] + '\n';
            var payload = row.data[1];
            var payloadPreview = payload.length > 80 ? payload.substring(0, 80) + '...' : payload;
            previewText += '  -> HTTP负载: ' + payloadPreview + '\n';
        }
        previewText += '\n';
    }

    csvPreviewContent.textContent = previewText;
    csvTextPreview.style.display = 'block';
}

/**
 * 显示CSV文件信息
 */
function showCsvFileInfo(file) {
    const csvFileInfo = document.getElementById('csvFileInfo');
    const fileName = document.getElementById('csvFileName');
    const fileSize = document.getElementById('csvFileSize');

    if (csvFileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        csvFileInfo.style.display = 'block';
    }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 处理拖拽上传
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

/**
 * 处理文件拖拽放置
 */
function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
            selectedCsvFile = file;
            showCsvFileInfo(file);

            // 更新文件输入框
            const csvFileInput = document.getElementById('csvFile');
            if (csvFileInput) {
                // 注意：由于安全限制，我们不能直接设置文件输入框的值
                // 但我们可以显示文件信息
            }
        } else {
            showAlert('请选择CSV格式的文件', 'error');
        }
    }
}

// 全局函数声明
window.handleCsvFileSelect = handleCsvFileSelect;
window.handleCsvTextFileSelect = handleCsvTextFileSelect;
window.toggleCsvUploadMode = function() {
    const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    const textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    const csvFileUpload = document.getElementById('csvFileUpload');
    const csvTextUpload = document.getElementById('csvTextUpload');

    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        if (csvFileUpload) csvFileUpload.style.display = 'block';
        if (csvTextUpload) csvTextUpload.style.display = 'none';

        // 重置文本上传状态
        csvTextContent = null;
        csvTextData = [];
        const csvTextFileInfo = document.getElementById('csvTextFileInfo');
        const csvTextPreview = document.getElementById('csvTextPreview');
        if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
        if (csvTextPreview) csvTextPreview.style.display = 'none';

    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        if (csvFileUpload) csvFileUpload.style.display = 'none';
        if (csvTextUpload) csvTextUpload.style.display = 'block';

        // 重置文件上传状态
        selectedCsvFile = null;
        const csvFileInfo = document.getElementById('csvFileInfo');
        if (csvFileInfo) csvFileInfo.style.display = 'none';
    }
};

/**
 * 下载CSV模板文件
 */
function downloadCsvTemplate() {
    // 创建CSV模板内容
    var csvTemplate = '测试项目名称,HTTP负载\n' +
        '"示例GET请求","GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: Mozilla/5.0\\r\\nAccept: application/json\\r\\n\\r\\n"\n' +
        '"示例POST请求","POST /api/data HTTP/1.1\\r\\nHost: example.com\\r\\nContent-Type: application/json\\r\\nContent-Length: 25\\r\\n\\r\\n{""key"": ""value""}"';

    // 创建Blob对象
    var blob = new Blob([csvTemplate], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    var link = document.createElement('a');
    var url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'http_load_template.csv');
    link.style.visibility = 'hidden';

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 确保所有必要的函数都暴露到全局作用域
window.toggleInputMode = window.toggleInputMode || toggleInputMode;
window.toggleCsvUploadMode = window.toggleCsvUploadMode || function() {
    const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    const textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    const csvFileUpload = document.getElementById('csvFileUpload');
    const csvTextUpload = document.getElementById('csvTextUpload');

    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        if (csvFileUpload) csvFileUpload.style.display = 'block';
        if (csvTextUpload) csvTextUpload.style.display = 'none';

        // 重置文本上传状态
        csvTextContent = null;
        csvTextData = [];
        const csvTextFileInfo = document.getElementById('csvTextFileInfo');
        const csvTextPreview = document.getElementById('csvTextPreview');
        if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
        if (csvTextPreview) csvTextPreview.style.display = 'none';

    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        if (csvFileUpload) csvFileUpload.style.display = 'none';
        if (csvTextUpload) csvTextUpload.style.display = 'block';

        // 重置文件上传状态
        selectedCsvFile = null;
        const csvFileInfo = document.getElementById('csvFileInfo');
        if (csvFileInfo) csvFileInfo.style.display = 'none';
    }
};
window.handleCsvFileSelect = handleCsvFileSelect;
window.handleCsvTextFileSelect = handleCsvTextFileSelect;
window.downloadCsvTemplate = downloadCsvTemplate;
window.resetForm = resetForm;
window.downloadResult = downloadResult;
window.viewResults = viewResults;
