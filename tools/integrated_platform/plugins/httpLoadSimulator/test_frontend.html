<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV文本上传功能测试</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            <i class="fas fa-file-csv"></i>
            CSV文本上传功能测试
        </h1>
        <p>测试新增的CSV文本上传功能，验证前台界面和后台接口的集成。</p>

        <!-- CSV上传方式选择测试 -->
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-toggle-on"></i>
                CSV上传方式选择
            </div>
            
            <div class="csv-upload-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="csvUploadMode" value="file" checked onchange="toggleCsvUploadMode()">
                    <span class="radio-label">
                        <i class="fas fa-file-upload"></i>
                        文件上传（二进制）
                    </span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="csvUploadMode" value="text" onchange="toggleCsvUploadMode()">
                    <span class="radio-label">
                        <i class="fas fa-file-alt"></i>
                        文本上传（推荐）
                    </span>
                </label>
            </div>
            <div class="form-hint">文本上传方式能更好地处理编码和特殊字符问题</div>
        </div>

        <!-- 文件上传方式测试 -->
        <div id="csvFileUpload" class="test-section">
            <div class="test-title">
                <i class="fas fa-file-csv"></i>
                文件上传方式（传统）
            </div>
            
            <div class="file-upload-area" id="csvUploadArea">
                <input type="file" id="csvFile" accept=".csv" onchange="handleCsvFileSelect(event)">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>点击选择CSV文件或拖拽文件到此处</p>
                    <small>支持.csv格式文件，最大10MB</small>
                </div>
            </div>
            
            <div id="csvFileInfo" style="display: none; margin-top: 10px;">
                <div class="alert success">
                    <i class="fas fa-check-circle"></i>
                    <span>文件已选择</span>
                </div>
                <div class="file-details">
                    <span class="file-name" id="csvFileName">文件名</span>
                    <span class="file-size" id="csvFileSize">文件大小</span>
                </div>
            </div>
        </div>

        <!-- 文本上传方式测试 -->
        <div id="csvTextUpload" class="test-section" style="display: none;">
            <div class="test-title">
                <i class="fas fa-file-alt"></i>
                文本上传方式（推荐）
            </div>
            
            <div class="csv-text-upload-area" id="csvTextUploadArea">
                <input type="file" id="csvTextFile" accept=".csv" onchange="handleCsvTextFileSelect(event)">
                <div class="upload-content">
                    <i class="fas fa-file-alt"></i>
                    <p>点击选择CSV文件或拖拽文件到此处</p>
                    <small>文件将以文本方式读取，自动处理编码问题</small>
                </div>
            </div>
            
            <!-- 文本上传文件信息 -->
            <div id="csvTextFileInfo" style="display: none; margin-top: 10px;">
                <div class="alert success">
                    <i class="fas fa-check-circle"></i>
                    <span>文件已读取</span>
                </div>
                <div class="file-details">
                    <div class="detail-row">
                        <span class="detail-label">文件名：</span>
                        <span class="detail-value" id="csvTextFileName">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">文件大小：</span>
                        <span class="detail-value" id="csvTextFileSize">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">检测到行数：</span>
                        <span class="detail-value" id="csvTextLineCount">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">测试项目数：</span>
                        <span class="detail-value" id="csvTextProjectCount">-</span>
                    </div>
                </div>
            </div>

            <!-- CSV内容预览 -->
            <div id="csvTextPreview" style="display: none; margin-top: 15px;">
                <div class="test-title">
                    <i class="fas fa-eye"></i>
                    内容预览（前5行）
                </div>
                <div class="csv-preview-container">
                    <pre id="csvPreviewContent" class="csv-preview-content"></pre>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-play"></i>
                功能测试
            </div>
            
            <button id="testBtn" class="btn btn-primary" onclick="testUploadFunction()">
                <i class="fas fa-test-tube"></i>
                测试上传功能
            </button>
            
            <button class="btn btn-secondary" onclick="resetAll()" style="margin-left: 10px;">
                <i class="fas fa-redo"></i>
                重置所有
            </button>
        </div>

        <!-- 测试结果 -->
        <div id="testResults" class="test-section" style="display: none;">
            <div class="test-title">
                <i class="fas fa-clipboard-check"></i>
                测试结果
            </div>
            <div id="testOutput"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedCsvFile = null;
        let csvTextContent = null;
        let csvTextData = [];
        let csvUploadMode = 'file';

        // 引入必要的函数
        function showMessage(message, type) {
            const alertClass = type === 'error' ? 'error' : 'success';
            const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
            
            const alert = document.createElement('div');
            alert.className = `alert ${alertClass}`;
            alert.innerHTML = `<i class="${icon}"></i><span>${message}</span>`;
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 3000);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 测试函数
        function testUploadFunction() {
            const testOutput = document.getElementById('testOutput');
            const testResults = document.getElementById('testResults');
            
            let results = '<h4>测试结果：</h4>';
            
            if (csvUploadMode === 'text') {
                if (csvTextContent) {
                    results += '<div class="alert success"><i class="fas fa-check"></i>文本上传模式：文件已读取</div>';
                    results += `<p><strong>文件内容长度：</strong>${csvTextContent.length} 字符</p>`;
                    results += `<p><strong>解析项目数：</strong>${csvTextData.length - 1} 个</p>`;
                    results += '<p><strong>编码处理：</strong>浏览器自动处理 ✓</p>';
                    results += '<p><strong>特殊字符：</strong>完整保持 ✓</p>';
                } else {
                    results += '<div class="alert error"><i class="fas fa-times"></i>文本上传模式：未选择文件</div>';
                }
            } else {
                if (selectedCsvFile) {
                    results += '<div class="alert success"><i class="fas fa-check"></i>文件上传模式：文件已选择</div>';
                    results += `<p><strong>文件大小：</strong>${formatFileSize(selectedCsvFile.size)}</p>`;
                    results += '<p><strong>编码处理：</strong>需要后台检测</p>';
                } else {
                    results += '<div class="alert error"><i class="fas fa-times"></i>文件上传模式：未选择文件</div>';
                }
            }
            
            testOutput.innerHTML = results;
            testResults.style.display = 'block';
        }

        function resetAll() {
            // 重置文件上传
            selectedCsvFile = null;
            const csvFile = document.getElementById('csvFile');
            const csvFileInfo = document.getElementById('csvFileInfo');
            if (csvFile) csvFile.value = '';
            if (csvFileInfo) csvFileInfo.style.display = 'none';
            
            // 重置文本上传
            csvTextContent = null;
            csvTextData = [];
            const csvTextFile = document.getElementById('csvTextFile');
            const csvTextFileInfo = document.getElementById('csvTextFileInfo');
            const csvTextPreview = document.getElementById('csvTextPreview');
            if (csvTextFile) csvTextFile.value = '';
            if (csvTextFileInfo) csvTextFileInfo.style.display = 'none';
            if (csvTextPreview) csvTextPreview.style.display = 'none';
            
            // 隐藏测试结果
            const testResults = document.getElementById('testResults');
            if (testResults) testResults.style.display = 'none';
            
            // 重置为文件上传模式
            const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
            if (fileMode) {
                fileMode.checked = true;
                toggleCsvUploadMode();
            }
        }
    </script>
    
    <!-- 引入实际的JavaScript文件 -->
    <script src="static/js/create_task.js"></script>
</body>
</html>
