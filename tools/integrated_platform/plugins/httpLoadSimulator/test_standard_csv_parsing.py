#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标准CSV库解析功能
"""

import os
import tempfile
import csv
import chardet
from io import BytesIO

def create_problematic_csv_file():
    """创建包含HTTP负载换行符的CSV文件"""
    
    # 使用标准CSV库创建正确格式的CSV文件
    csv_data = [
        ['测试项目名称', 'HTTP负载'],
        [
            'JSON请求测试',
            'POST /api/data HTTP/1.1\r\nContent-Type: application/json\r\nContent-Length: 45\r\n\r\n{"name":"test","value":123,"status":"active"}'
        ],
        [
            '文件上传测试',
            'POST /upload HTTP/1.1\r\nContent-Type: multipart/form-data; boundary=----WebKit\r\nContent-Length: 234\r\n\r\n------WebKit\r\nContent-Disposition: form-data; name="file"; filename="test.txt"\r\nContent-Type: text/plain\r\n\r\nHello World!\r\n------WebKit--'
        ],
        [
            '复杂Accept头测试',
            'GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n\r\n'
        ],
        [
            '中文测试,包含逗号',
            'POST /中文路径 HTTP/1.1\r\nHost: 测试服务器.com\r\nContent-Type: application/json; charset=utf-8\r\n\r\n{"消息":"你好,世界!","状态":"成功"}'
        ]
    ]
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='gbk', newline='')
    
    try:
        writer = csv.writer(temp_file, quoting=csv.QUOTE_ALL)
        for row in csv_data:
            writer.writerow(row)
        temp_file_path = temp_file.name
    finally:
        temp_file.close()
    
    return temp_file_path

def test_standard_csv_parsing():
    """测试标准CSV库解析"""
    print("=== 测试标准CSV库解析功能 ===")
    print()
    
    # 创建测试文件
    csv_file_path = create_problematic_csv_file()
    print(f"创建测试CSV文件: {csv_file_path}")
    
    try:
        # 1. 检查原始文件内容
        print("\n1. 原始文件内容分析:")
        with open(csv_file_path, 'rb') as f:
            raw_data = f.read()
        
        print(f"   文件大小: {len(raw_data)} 字节")
        crlf_count = raw_data.count(b'\r\n')
        comma_count = raw_data.count(b',')
        print(f"   包含\\r\\n数量: {crlf_count}")
        print(f"   包含逗号数量: {comma_count}")
        
        # 2. 检测编码
        print("\n2. 编码检测:")
        encoding_result = chardet.detect(raw_data)
        detected_encoding = encoding_result['encoding']
        confidence = encoding_result['confidence']
        print(f"   检测编码: {detected_encoding} (置信度: {confidence:.3f})")
        
        # 3. 使用标准CSV库解析
        print("\n3. 标准CSV库解析:")
        csv_data = parse_with_standard_csv(csv_file_path, detected_encoding)
        
        print(f"   解析结果: {len(csv_data)} 个测试项目")
        
        # 4. 验证解析结果
        print("\n4. 解析结果验证:")
        for i, item in enumerate(csv_data):
            print(f"   项目 {i+1}:")
            print(f"     名称: {item['testName']}")
            print(f"     HTTP负载长度: {len(item['httpPayload'])} 字符")
            has_crlf = '\r\n' in item['httpPayload']
            has_comma = ',' in item['httpPayload']
            print(f"     包含\\r\\n: {'✓' if has_crlf else '✗'}")
            print(f"     包含逗号: {'✓' if has_comma else '✗'}")
            print(f"     负载预览: {item['httpPayload'][:80]}...")
            print()
    
    finally:
        # 清理临时文件
        if os.path.exists(csv_file_path):
            os.unlink(csv_file_path)

def parse_with_standard_csv(file_path, encoding):
    """使用标准CSV库解析文件"""
    csv_data = []
    
    try:
        with open(file_path, 'r', encoding=encoding, newline='') as csvfile:
            # 检测CSV方言
            sample = csvfile.read(1024)
            csvfile.seek(0)
            
            try:
                dialect = csv.Sniffer().sniff(sample)
                print(f"   检测到CSV方言: {dialect.__class__.__name__}")
            except:
                dialect = csv.excel
                print(f"   使用默认CSV方言: excel")
            
            # 创建CSV读取器
            reader = csv.reader(csvfile, dialect)
            
            # 处理每一行
            for row_index, row in enumerate(reader, 1):
                if row_index == 1:
                    # 标题行
                    print(f"   标题行: {row}")
                    continue
                
                if len(row) >= 2:
                    test_name = row[0].strip()
                    http_payload = row[1].strip()
                    
                    if test_name and http_payload:
                        csv_data.append({
                            'testName': test_name,
                            'httpPayload': http_payload,
                            'rowIndex': row_index
                        })
                        print(f"   ✓ 第 {row_index} 行解析成功")
                    else:
                        print(f"   ✗ 第 {row_index} 行数据不完整")
                else:
                    print(f"   ✗ 第 {row_index} 行列数不足: {len(row)}")
        
        return csv_data
        
    except Exception as e:
        print(f"   ✗ 解析失败: {str(e)}")
        return []

def test_encoding_detection():
    """测试编码检测功能"""
    print("=== 测试编码检测功能 ===")
    print()
    
    # 创建不同编码的测试文件
    test_encodings = ['utf-8', 'gbk', 'gb2312']
    
    for encoding in test_encodings:
        print(f"测试编码: {encoding}")
        
        # 创建测试内容
        test_content = [
            ['测试项目名称', 'HTTP负载'],
            ['项目测试', 'GET /api HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n'],
            ['负载测试', 'POST /data HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{"test":"data"}']
        ]
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding=encoding, newline='')
        
        try:
            writer = csv.writer(temp_file, quoting=csv.QUOTE_ALL)
            for row in test_content:
                writer.writerow(row)
            temp_file_path = temp_file.name
        finally:
            temp_file.close()
        
        try:
            # 检测编码
            with open(temp_file_path, 'rb') as f:
                raw_data = f.read()
            
            result = chardet.detect(raw_data)
            detected = result['encoding']
            confidence = result['confidence']
            
            print(f"   原始编码: {encoding}")
            print(f"   检测编码: {detected} (置信度: {confidence:.3f})")
            print(f"   检测正确: {'✓' if detected.lower().replace('-', '') == encoding.lower().replace('-', '') else '✗'}")
            
            # 尝试解析
            try:
                csv_data = parse_with_standard_csv(temp_file_path, detected)
                print(f"   解析结果: {len(csv_data)} 个项目")
            except Exception as e:
                print(f"   解析失败: {str(e)}")
            
            print()
        
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

def test_file_save_and_parse():
    """测试完整的保存和解析流程"""
    print("=== 测试完整的保存和解析流程 ===")
    print()
    
    # 模拟前台上传的二进制数据
    csv_content = '''测试项目名称,HTTP负载
"JSON请求","POST /api HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"items\\":[1,2,3]}"
"文件上传","POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data\\r\\n\\r\\nfile data with\\r\\nnewlines"
"复杂请求","GET /api HTTP/1.1\\r\\nAccept: text/html,application/xml;q=0.9,*/*;q=0.8\\r\\n\\r\\n"'''
    
    # 转换为二进制数据（模拟上传）
    binary_data = csv_content.encode('gbk')
    
    print(f"模拟上传数据大小: {len(binary_data)} 字节")
    crlf_count = binary_data.count(b'\r\n')
    print(f"包含\\r\\n数量: {crlf_count}")
    
    # 模拟Flask的FileStorage对象
    class MockFileUpload:
        def __init__(self, data):
            self.data = BytesIO(data)
        
        def seek(self, pos):
            self.data.seek(pos)
        
        def read(self, size=None):
            return self.data.read(size)
    
    mock_file = MockFileUpload(binary_data)
    
    # 创建临时文件路径
    temp_file_path = tempfile.mktemp(suffix='.csv')
    
    try:
        print("\\n1. 保存原始文件:")
        success = save_raw_csv_file(mock_file, temp_file_path)
        print(f"   保存结果: {'✓' if success else '✗'}")
        
        if success:
            print("\\n2. 解析保存的文件:")
            csv_data = parse_standard_csv_file(temp_file_path)
            print(f"   解析结果: {len(csv_data)} 个项目")
            
            print("\\n3. 验证数据完整性:")
            for i, item in enumerate(csv_data):
                print(f"   项目 {i+1}: {item['testName']}")
                has_http = 'HTTP/1.1' in item['httpPayload']
                has_crlf = '\r\n' in item['httpPayload']
                print(f"     HTTP负载正确: {'✓' if has_http else '✗'}")
                print(f"     换行符保持: {'✓' if has_crlf else '✗'}")
    
    finally:
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def save_raw_csv_file(csv_file, file_path):
    """保存原始CSV文件"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        csv_file.seek(0)
        raw_data = csv_file.read()
        
        with open(file_path, 'wb') as f:
            f.write(raw_data)
        
        print(f"   原始文件已保存: {file_path}")
        return True
        
    except Exception as e:
        print(f"   保存失败: {str(e)}")
        return False

def parse_standard_csv_file(file_path):
    """解析标准CSV文件"""
    try:
        # 检测编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(8192)
        
        result = chardet.detect(raw_data)
        encoding = result['encoding'] if result['encoding'] else 'utf-8'
        print(f"   检测编码: {encoding}")
        
        # 解析CSV
        csv_data = []
        with open(file_path, 'r', encoding=encoding, newline='') as csvfile:
            reader = csv.reader(csvfile)
            
            for row_index, row in enumerate(reader, 1):
                if row_index == 1:  # 跳过标题行
                    continue
                
                if len(row) >= 2:
                    csv_data.append({
                        'testName': row[0].strip(),
                        'httpPayload': row[1].strip(),
                        'rowIndex': row_index
                    })
        
        return csv_data
        
    except Exception as e:
        print(f"   解析失败: {str(e)}")
        return []

if __name__ == '__main__':
    test_standard_csv_parsing()
    test_encoding_detection()
    test_file_save_and_parse()
