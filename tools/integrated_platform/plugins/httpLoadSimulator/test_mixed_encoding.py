#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试混合编码CSV文件处理功能
"""

import os
import sys
import tempfile
from io import BytesIO

def create_mixed_encoding_csv():
    """创建包含混合编码的CSV测试文件"""
    
    # 标题行（UTF-8编码）
    header = "测试项目名称,HTTP负载\n"
    header_bytes = header.encode('utf-8')
    
    # 第一行：中文项目名称（GBK编码）+ ASCII HTTP负载
    chinese_name = "项目,负载测试"
    http_payload = 'GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: TestAgent\r\n\r\n'
    
    # 将中文部分编码为GBK，HTTP部分保持ASCII
    chinese_bytes = chinese_name.encode('gbk')
    http_bytes = http_payload.encode('ascii')
    
    # 构造CSV行：用引号包围字段，用逗号分隔
    line1 = b'"' + chinese_bytes + b'","' + http_bytes + b'"\n'
    
    # 第二行：更复杂的混合编码
    chinese_name2 = "文件上传_特征隐藏webshell_绕过检测"
    http_payload2 = '''POST /upload.php HTTP/1.1\r\nHost: target.com\r\nContent-Type: multipart/form-data\r\n\r\n--boundary\r\nContent-Disposition: form-data; name="file"; filename="test.php"\r\n\r\n<?php echo "hello"; ?>\r\n--boundary--'''
    
    chinese_bytes2 = chinese_name2.encode('gbk')
    http_bytes2 = http_payload2.encode('ascii')
    
    line2 = b'"' + chinese_bytes2 + b'","' + http_bytes2 + b'"\n'
    
    # 组合所有数据
    full_csv = header_bytes + line1 + line2
    
    return full_csv

def test_mixed_encoding_processing():
    """测试混合编码处理功能"""
    print("=== 测试混合编码CSV处理功能 ===")
    print()
    
    # 创建测试数据
    csv_data = create_mixed_encoding_csv()
    print(f"创建混合编码CSV数据，总大小: {len(csv_data)} 字节")
    
    # 分析数据结构
    print("\n1. 数据结构分析:")
    lines = csv_data.split(b'\n')
    for i, line in enumerate(lines):
        if line.strip():
            print(f"   行 {i+1}: {len(line)} 字节")
            # 尝试显示前50个字符的十六进制
            hex_preview = line[:50].hex().upper()
            print(f"        十六进制: {hex_preview}...")
    
    # 测试逐行解码
    print("\n2. 逐行编码检测:")
    import chardet
    
    for i, line in enumerate(lines):
        if line.strip():
            # 检测编码
            encoding_result = chardet.detect(line)
            detected_encoding = encoding_result['encoding']
            confidence = encoding_result['confidence']
            
            print(f"   行 {i+1}:")
            print(f"     检测编码: {detected_encoding} (置信度: {confidence:.2f})")
            
            # 尝试不同编码解码
            encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'latin1']
            for enc in encodings_to_try:
                try:
                    decoded = line.decode(enc)
                    print(f"     {enc:>8}: ✓ {decoded[:60]}...")
                    break
                except UnicodeDecodeError:
                    print(f"     {enc:>8}: ✗ 解码失败")
    
    # 测试我们的处理函数
    print("\n3. 测试新的处理逻辑:")
    
    # 模拟文件对象
    class MockFile:
        def __init__(self, data):
            self.data = BytesIO(data)
        
        def seek(self, pos):
            self.data.seek(pos)
        
        def read(self):
            return self.data.read()
    
    mock_file = MockFile(csv_data)
    
    # 测试分行功能
    print("   测试行分割...")
    lines = split_csv_lines_test(csv_data)
    print(f"   ✓ 分割得到 {len(lines)} 行")
    
    # 测试逐行解码
    print("   测试逐行解码...")
    for i, line_bytes in enumerate(lines):
        decoded_text = decode_line_with_multiple_encodings_test(line_bytes)
        if decoded_text:
            print(f"   ✓ 行 {i+1}: {decoded_text[:50]}...")
        else:
            print(f"   ✗ 行 {i+1}: 解码失败")
    
    print("\n4. 完整处理测试:")
    csv_result = parse_raw_csv_data_test(csv_data)
    if csv_result:
        print(f"   ✓ 成功解析 {len(csv_result)} 个测试项目")
        for i, item in enumerate(csv_result):
            print(f"   项目 {i+1}: {item['testName']}")
            print(f"   HTTP负载: {len(item['httpPayload'])} 字符")
    else:
        print("   ✗ 解析失败")

def split_csv_lines_test(raw_data: bytes):
    """测试行分割功能"""
    line_separators = [b'\r\n', b'\n', b'\r']
    
    for separator in line_separators:
        if separator in raw_data:
            lines = raw_data.split(separator)
            lines = [line for line in lines if line.strip()]
            if len(lines) >= 2:
                return lines
    
    if raw_data.strip():
        return [raw_data]
    
    return []

def decode_line_with_multiple_encodings_test(line_bytes: bytes) -> str:
    """测试多编码解码功能"""
    import chardet
    
    if not line_bytes:
        return ""
    
    # 检测编码
    encoding_result = chardet.detect(line_bytes)
    detected_encoding = encoding_result['encoding'] if encoding_result['encoding'] else 'utf-8'
    
    # 尝试多种编码
    encodings_to_try = [detected_encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
    
    # 去重
    seen = set()
    unique_encodings = []
    for enc in encodings_to_try:
        if enc and enc not in seen:
            seen.add(enc)
            unique_encodings.append(enc)
    
    # 尝试解码
    for encoding in unique_encodings:
        try:
            decoded_text = line_bytes.decode(encoding)
            # 简单验证
            if decoded_text and (',' in decoded_text or any(ord(c) >= 32 for c in decoded_text)):
                return decoded_text
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    # 最后尝试错误处理
    try:
        return line_bytes.decode('utf-8', errors='replace')
    except:
        return line_bytes.decode('latin1', errors='replace')

def parse_raw_csv_data_test(raw_data: bytes):
    """测试完整的CSV解析功能"""
    import csv
    import io
    
    csv_data = []
    
    # 分行
    lines = split_csv_lines_test(raw_data)
    if not lines:
        return []
    
    # 逐行处理
    for line_index, line_bytes in enumerate(lines):
        if line_index == 0:
            # 跳过标题行
            continue
        
        # 解码行
        line_text = decode_line_with_multiple_encodings_test(line_bytes)
        if not line_text:
            continue
        
        # 解析CSV行
        try:
            string_io = io.StringIO(line_text)
            reader = csv.reader(string_io, quoting=csv.QUOTE_ALL)
            row = next(reader)
            
            if len(row) >= 2:
                test_name = row[0].strip()
                http_payload = row[1].strip()
                
                if test_name and http_payload:
                    csv_data.append({
                        'testName': test_name,
                        'httpPayload': http_payload,
                        'rowIndex': line_index + 1
                    })
        except Exception as e:
            # 尝试简单分割
            try:
                parts = line_text.split(',', 1)
                if len(parts) >= 2:
                    test_name = parts[0].strip().strip('"')
                    http_payload = parts[1].strip().strip('"')
                    if test_name and http_payload:
                        csv_data.append({
                            'testName': test_name,
                            'httpPayload': http_payload,
                            'rowIndex': line_index + 1
                        })
            except:
                pass
    
    return csv_data

if __name__ == '__main__':
    test_mixed_encoding_processing()
