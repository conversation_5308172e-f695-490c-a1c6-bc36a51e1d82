测试项目名称,HTTP负载
"简单GET请求","GET /api/test HTTP/1.1
Host: example.com
User-Agent: TestAgent/1.0

"
"POST请求带JSON","POST /api/data HTTP/1.1
Host: example.com
Content-Type: application/json
Content-Length: 25

{""name"":""test"",""value"":123}"
"包含中文的请求","GET /中文路径/测试 HTTP/1.1
Host: 测试.com
Accept: text/html

"
"复杂HTTP请求","GET /complex HTTP/1.1
Host: example.com
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3
Accept-Encoding: gzip, deflate
Connection: keep-alive
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9

"
"文件上传请求","POST /upload HTTP/1.1
Host: example.com
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Length: 234

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name=""file""; filename=""test.txt""
Content-Type: text/plain

Hello World!
------WebKitFormBoundary7MA4YWxkTrZu0gW--
"
