<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV上传验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .upload-mode-selector {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .radio-option {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }
        .radio-option:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .radio-option input[type="radio"] {
            margin-right: 10px;
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8fafc;
            margin: 15px 0;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        .btn-success {
            background-color: #48bb78;
            color: white;
        }
        .btn-danger {
            background-color: #f56565;
            color: white;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .file-info {
            background: #e6f3ff;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSV上传验证修复测试</h1>
        <p>测试修复后的CSV上传验证逻辑，确保文本上传方式不会被错误拦截。</p>

        <!-- 测试1：文件上传方式 -->
        <div class="test-section">
            <div class="test-title">测试1：文件上传方式验证</div>
            
            <div class="upload-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="testMode1" value="file" checked>
                    <span>文件上传（二进制）</span>
                </label>
            </div>
            
            <div class="upload-area">
                <input type="file" id="testFile1" accept=".csv">
                <p>选择CSV文件进行文件上传测试</p>
            </div>
            
            <button class="btn btn-primary" onclick="testFileUploadValidation()">
                测试文件上传验证
            </button>
            
            <div id="testResult1" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试2：文本上传方式 -->
        <div class="test-section">
            <div class="test-title">测试2：文本上传方式验证</div>
            
            <div class="upload-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="testMode2" value="text" checked>
                    <span>文本上传（推荐）</span>
                </label>
            </div>
            
            <div class="upload-area">
                <input type="file" id="testFile2" accept=".csv">
                <p>选择CSV文件进行文本上传测试</p>
            </div>
            
            <button class="btn btn-primary" onclick="testTextUploadValidation()">
                测试文本上传验证
            </button>
            
            <div id="testResult2" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试3：模式切换测试 -->
        <div class="test-section">
            <div class="test-title">测试3：模式切换验证</div>
            
            <div class="upload-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="testMode3" value="file" checked onchange="switchTestMode()">
                    <span>文件上传</span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="testMode3" value="text" onchange="switchTestMode()">
                    <span>文本上传</span>
                </label>
            </div>
            
            <div id="fileUploadTest" class="upload-area">
                <input type="file" id="testFile3a" accept=".csv">
                <p>文件上传模式</p>
            </div>
            
            <div id="textUploadTest" class="upload-area" style="display: none;">
                <input type="file" id="testFile3b" accept=".csv">
                <p>文本上传模式</p>
            </div>
            
            <button class="btn btn-primary" onclick="testSwitchValidation()">
                测试当前模式验证
            </button>
            
            <div id="testResult3" class="test-result" style="display: none;"></div>
        </div>

        <!-- 测试4：综合测试 -->
        <div class="test-section">
            <div class="test-title">测试4：综合功能测试</div>
            
            <button class="btn btn-success" onclick="runAllTests()">
                运行所有测试
            </button>
            <button class="btn btn-danger" onclick="clearAllTests()">
                清除测试结果
            </button>
            
            <div id="allTestResults" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟全局变量
        var selectedCsvFile = null;
        var csvTextContent = null;
        var csvUploadMode = 'file';
        var currentInputMode = 'csv';

        // 模拟验证函数
        function validateCsvUpload() {
            if (currentInputMode === 'csv') {
                if (csvUploadMode === 'text') {
                    // 文本上传方式
                    if (!csvTextContent) {
                        return { valid: false, message: '请选择CSV文件进行文本上传' };
                    }
                } else {
                    // 文件上传方式
                    if (!selectedCsvFile) {
                        return { valid: false, message: '请选择CSV文件进行文件上传' };
                    }
                }
            }
            return { valid: true, message: '验证通过' };
        }

        function testFileUploadValidation() {
            csvUploadMode = 'file';
            selectedCsvFile = null;
            csvTextContent = null;
            
            var file = document.getElementById('testFile1').files[0];
            if (file) {
                selectedCsvFile = file;
            }
            
            var result = validateCsvUpload();
            var resultDiv = document.getElementById('testResult1');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result ' + (result.valid ? 'success' : 'error');
            resultDiv.innerHTML = 
                '文件上传模式测试结果:\n' +
                '- 上传模式: ' + csvUploadMode + '\n' +
                '- 选择的文件: ' + (selectedCsvFile ? selectedCsvFile.name : '无') + '\n' +
                '- 文本内容: ' + (csvTextContent ? '有' : '无') + '\n' +
                '- 验证结果: ' + (result.valid ? '✅ 通过' : '❌ 失败') + '\n' +
                '- 提示信息: ' + result.message;
        }

        function testTextUploadValidation() {
            csvUploadMode = 'text';
            selectedCsvFile = null;
            csvTextContent = null;
            
            var file = document.getElementById('testFile2').files[0];
            if (file) {
                // 模拟文本读取
                var reader = new FileReader();
                reader.onload = function(e) {
                    csvTextContent = e.target.result;
                    
                    var result = validateCsvUpload();
                    var resultDiv = document.getElementById('testResult2');
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result ' + (result.valid ? 'success' : 'error');
                    resultDiv.innerHTML = 
                        '文本上传模式测试结果:\n' +
                        '- 上传模式: ' + csvUploadMode + '\n' +
                        '- 选择的文件: ' + (selectedCsvFile ? selectedCsvFile.name : '无') + '\n' +
                        '- 文本内容: ' + (csvTextContent ? '有 (' + csvTextContent.length + ' 字符)' : '无') + '\n' +
                        '- 验证结果: ' + (result.valid ? '✅ 通过' : '❌ 失败') + '\n' +
                        '- 提示信息: ' + result.message;
                };
                reader.readAsText(file);
            } else {
                var result = validateCsvUpload();
                var resultDiv = document.getElementById('testResult2');
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = 
                    '文本上传模式测试结果:\n' +
                    '- 上传模式: ' + csvUploadMode + '\n' +
                    '- 选择的文件: 无\n' +
                    '- 文本内容: 无\n' +
                    '- 验证结果: ❌ 失败\n' +
                    '- 提示信息: ' + result.message;
            }
        }

        function switchTestMode() {
            var fileMode = document.querySelector('input[name="testMode3"][value="file"]');
            var textMode = document.querySelector('input[name="testMode3"][value="text"]');
            var fileUploadDiv = document.getElementById('fileUploadTest');
            var textUploadDiv = document.getElementById('textUploadTest');
            
            if (fileMode.checked) {
                csvUploadMode = 'file';
                fileUploadDiv.style.display = 'block';
                textUploadDiv.style.display = 'none';
            } else {
                csvUploadMode = 'text';
                fileUploadDiv.style.display = 'none';
                textUploadDiv.style.display = 'block';
            }
        }

        function testSwitchValidation() {
            selectedCsvFile = null;
            csvTextContent = null;
            
            if (csvUploadMode === 'file') {
                var file = document.getElementById('testFile3a').files[0];
                if (file) selectedCsvFile = file;
            } else {
                var file = document.getElementById('testFile3b').files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        csvTextContent = e.target.result;
                        showSwitchResult();
                    };
                    reader.readAsText(file);
                    return;
                }
            }
            
            showSwitchResult();
        }

        function showSwitchResult() {
            var result = validateCsvUpload();
            var resultDiv = document.getElementById('testResult3');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result ' + (result.valid ? 'success' : 'error');
            resultDiv.innerHTML = 
                '模式切换测试结果:\n' +
                '- 当前模式: ' + csvUploadMode + '\n' +
                '- 文件变量: ' + (selectedCsvFile ? selectedCsvFile.name : '无') + '\n' +
                '- 文本变量: ' + (csvTextContent ? '有 (' + csvTextContent.length + ' 字符)' : '无') + '\n' +
                '- 验证结果: ' + (result.valid ? '✅ 通过' : '❌ 失败') + '\n' +
                '- 提示信息: ' + result.message;
        }

        function runAllTests() {
            var results = [];
            
            // 测试1：文件上传无文件
            csvUploadMode = 'file';
            selectedCsvFile = null;
            csvTextContent = null;
            var result1 = validateCsvUpload();
            results.push('测试1 - 文件上传无文件: ' + (result1.valid ? '✅ 意外通过' : '❌ 正确失败'));
            
            // 测试2：文本上传无内容
            csvUploadMode = 'text';
            selectedCsvFile = null;
            csvTextContent = null;
            var result2 = validateCsvUpload();
            results.push('测试2 - 文本上传无内容: ' + (result2.valid ? '✅ 意外通过' : '❌ 正确失败'));
            
            // 测试3：文件上传有文件
            csvUploadMode = 'file';
            selectedCsvFile = { name: 'test.csv' }; // 模拟文件对象
            csvTextContent = null;
            var result3 = validateCsvUpload();
            results.push('测试3 - 文件上传有文件: ' + (result3.valid ? '✅ 正确通过' : '❌ 意外失败'));
            
            // 测试4：文本上传有内容
            csvUploadMode = 'text';
            selectedCsvFile = null;
            csvTextContent = 'test,data\nrow1,value1';
            var result4 = validateCsvUpload();
            results.push('测试4 - 文本上传有内容: ' + (result4.valid ? '✅ 正确通过' : '❌ 意外失败'));
            
            var resultDiv = document.getElementById('allTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = '综合测试结果:\n' + results.join('\n');
        }

        function clearAllTests() {
            var resultDivs = document.querySelectorAll('.test-result');
            for (var i = 0; i < resultDivs.length; i++) {
                resultDivs[i].style.display = 'none';
            }
            
            // 重置变量
            selectedCsvFile = null;
            csvTextContent = null;
            csvUploadMode = 'file';
            
            // 重置文件输入
            var fileInputs = document.querySelectorAll('input[type="file"]');
            for (var i = 0; i < fileInputs.length; i++) {
                fileInputs[i].value = '';
            }
        }
    </script>
</body>
</html>
