#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能CSV解析功能，处理HTTP负载中的特殊字符
"""

import os
import tempfile

def create_problematic_csv_content():
    """创建包含问题字符的CSV内容"""
    
    # 测试用例1：HTTP负载包含双引号和逗号
    test_cases = [
        # 标题行
        '测试项目名称,HTTP负载',
        
        # 测试用例1：包含双引号的JSON负载
        '"JSON请求测试","POST /api/data HTTP/1.1\\r\\nContent-Type: application/json\\r\\nContent-Length: 45\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"value\\":123,\\"status\\":\\"active\\"}"',

        # 测试用例2：包含换行符的multipart负载
        '"文件上传测试","POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data; boundary=----WebKitFormBoundary\\r\\nContent-Length: 234\\r\\n\\r\\n------WebKitFormBoundary\\r\\nContent-Disposition: form-data; name=\\"file\\"; filename=\\"test.txt\\"\\r\\nContent-Type: text/plain\\r\\n\\r\\nHello World!\\r\\n------WebKitFormBoundary--"',

        # 测试用例3：包含逗号的User-Agent
        '"复杂请求测试","GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\\r\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\\r\\n\\r\\n"',

        # 测试用例4：包含中文和特殊字符
        '"中文测试,包含逗号","POST /中文路径 HTTP/1.1\\r\\nHost: 测试服务器.com\\r\\nContent-Type: application/json; charset=utf-8\\r\\n\\r\\n{\\"消息\\":\\"你好,世界!\\",\\"状态\\":\\"成功\\"}"'
    ]
    
    return '\n'.join(test_cases)

def test_smart_line_split():
    """测试智能行分割功能"""
    print("=== 测试智能行分割功能 ===")
    print()
    
    csv_content = create_problematic_csv_content()
    print("原始CSV内容:")
    print(csv_content)
    print()
    
    # 测试智能行分割
    lines = smart_line_split_test(csv_content)
    print(f"智能分割结果，共 {len(lines)} 行:")
    for i, line in enumerate(lines):
        print(f"行 {i+1}: {line}")
    print()

def test_smart_column_split():
    """测试智能列分割功能"""
    print("=== 测试智能列分割功能 ===")
    print()
    
    test_lines = [
        '"JSON请求测试","POST /api/data HTTP/1.1\\r\\nContent-Type: application/json\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"value\\":123}"',
        '"文件上传,包含逗号","POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data\\r\\n\\r\\nfile content"',
        '简单测试,GET /api HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n',
        '"复杂测试","GET /test HTTP/1.1\\r\\nUser-Agent: Mozilla/5.0 (compatible; test,bot)\\r\\n\\r\\n"'
    ]
    
    for i, line in enumerate(test_lines):
        print(f"测试行 {i+1}: {line}")
        result = smart_column_split_test(line)
        if result:
            test_name, http_payload = result
            print(f"  测试名称: {test_name}")
            print(f"  HTTP负载: {http_payload[:100]}...")
        else:
            print("  解析失败")
        print()

def test_complete_parsing():
    """测试完整的解析流程"""
    print("=== 测试完整解析流程 ===")
    print()
    
    # 创建临时CSV文件
    csv_content = create_problematic_csv_content()
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as f:
        f.write(csv_content)
        temp_file_path = f.name
    
    try:
        # 读取并解析
        with open(temp_file_path, 'rb') as f:
            raw_data = f.read()
        
        print(f"文件大小: {len(raw_data)} 字节")
        
        # 测试完整解析流程
        csv_data = parse_csv_complete_test(raw_data)
        
        print(f"解析结果，共 {len(csv_data)} 个测试项目:")
        for i, item in enumerate(csv_data):
            print(f"项目 {i+1}:")
            print(f"  名称: {item['testName']}")
            print(f"  HTTP负载长度: {len(item['httpPayload'])} 字符")
            print(f"  HTTP负载预览: {item['httpPayload'][:150]}...")
            print()
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def smart_line_split_test(text_data: str):
    """测试智能行分割"""
    lines = []
    current_line = ""
    in_quotes = False
    escape_next = False
    i = 0
    
    while i < len(text_data):
        char = text_data[i]
        
        if escape_next:
            current_line += char
            escape_next = False
        elif char == '\\\\':
            current_line += char
            escape_next = True
        elif char == '"':
            current_line += char
            in_quotes = not in_quotes
        elif char == '\\n' and not in_quotes:
            if current_line.strip():
                lines.append(current_line)
            current_line = ""
        elif char == '\\r':
            if i + 1 < len(text_data) and text_data[i + 1] == '\\n':
                if not in_quotes:
                    if current_line.strip():
                        lines.append(current_line)
                    current_line = ""
                    i += 1
                else:
                    current_line += char
            else:
                if not in_quotes:
                    if current_line.strip():
                        lines.append(current_line)
                    current_line = ""
                else:
                    current_line += char
        else:
            current_line += char
        
        i += 1
    
    if current_line.strip():
        lines.append(current_line)
    
    return lines

def smart_column_split_test(line_text: str):
    """测试智能列分割"""
    if not line_text or not line_text.strip():
        return None
    
    # 查找第一个非引号内的逗号
    in_quotes = False
    escape_next = False
    comma_pos = -1
    
    for i, char in enumerate(line_text):
        if escape_next:
            escape_next = False
            continue
        elif char == '\\\\':
            escape_next = True
        elif char == '"':
            in_quotes = not in_quotes
        elif char == ',' and not in_quotes:
            comma_pos = i
            break
    
    if comma_pos == -1:
        # 没有找到合适的逗号，尝试简单分割
        parts = line_text.split(',', 1)
        if len(parts) >= 2:
            return clean_csv_field_test(parts[0]), clean_csv_field_test(parts[1])
        return None
    
    # 按找到的逗号位置分割
    test_name = line_text[:comma_pos]
    http_payload = line_text[comma_pos + 1:]
    
    return clean_csv_field_test(test_name), clean_csv_field_test(http_payload)

def clean_csv_field_test(field: str) -> str:
    """清理CSV字段"""
    if not field:
        return ""
    
    field = field.strip()
    
    # 移除外层引号
    if len(field) >= 2 and field.startswith('"') and field.endswith('"'):
        field = field[1:-1]
        # 处理转义的双引号
        field = field.replace('""', '"')
    
    return field

def parse_csv_complete_test(raw_data: bytes):
    """完整的CSV解析测试"""
    import chardet
    
    # 安全解码
    encoding_result = chardet.detect(raw_data)
    detected_encoding = encoding_result['encoding'] if encoding_result['encoding'] else 'utf-8'
    
    encodings_to_try = [detected_encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
    
    text_data = None
    for encoding in encodings_to_try:
        try:
            text_data = raw_data.decode(encoding)
            break
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    if not text_data:
        return []
    
    # 智能行分割
    lines = smart_line_split_test(text_data)
    
    # 解析每行
    csv_data = []
    for i, line in enumerate(lines):
        if i == 0:  # 跳过标题行
            continue
        
        result = smart_column_split_test(line)
        if result:
            test_name, http_payload = result
            if test_name and http_payload:
                csv_data.append({
                    'testName': test_name,
                    'httpPayload': http_payload,
                    'rowIndex': i + 1
                })
    
    return csv_data

if __name__ == '__main__':
    test_smart_line_split()
    test_smart_column_split()
    test_complete_parsing()
