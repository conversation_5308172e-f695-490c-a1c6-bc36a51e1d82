#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编码修复 - 验证各种编码的CSV文件处理
"""

import os
import tempfile
import csv

def create_test_csv_files():
    """创建各种编码的测试CSV文件"""
    test_files = {}
    
    # 测试内容
    csv_content = [
        ['测试项目名称', 'HTTP负载'],
        ['GET请求测试', 'GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Mozilla/5.0\r\n\r\n'],
        ['POST请求测试', 'POST /api/data HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\nContent-Length: 25\r\n\r\n{"key": "value"}'],
        ['中文测试', 'GET /中文路径 HTTP/1.1\r\nHost: 测试服务器.com\r\n\r\n']
    ]
    
    # 创建不同编码的文件
    encodings_to_test = [
        ('utf-8', 'UTF-8编码'),
        ('gbk', 'GBK编码'),
        ('gb2312', 'GB2312编码'),
        ('utf-8-sig', 'UTF-8 BOM编码'),
        ('latin1', 'Latin1编码'),
    ]
    
    for encoding, description in encodings_to_test:
        try:
            # 创建临时文件
            fd, filepath = tempfile.mkstemp(suffix=f'_{encoding}.csv', prefix='test_')
            os.close(fd)
            
            # 写入CSV内容
            with open(filepath, 'w', encoding=encoding, newline='') as f:
                writer = csv.writer(f)
                writer.writerows(csv_content)
            
            test_files[encoding] = {
                'path': filepath,
                'description': description,
                'size': os.path.getsize(filepath)
            }
            
            print(f"✓ 创建 {description} 文件: {filepath} ({test_files[encoding]['size']} 字节)")
            
        except Exception as e:
            print(f"✗ 创建 {description} 文件失败: {str(e)}")
    
    return test_files

def create_problematic_csv_file():
    """创建有问题的CSV文件（模拟0xcf字节错误）"""
    try:
        fd, filepath = tempfile.mkstemp(suffix='_problematic.csv', prefix='test_')
        
        # 创建包含问题字节的内容
        problematic_content = b'\xcf\xbb\xbf'  # 这会导致UTF-8解码错误
        problematic_content += '测试项目名称,HTTP负载\n'.encode('gbk')
        problematic_content += '"问题测试","GET /test HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n"\n'.encode('gbk')
        
        with open(filepath, 'wb') as f:
            f.write(problematic_content)
        
        print(f"✓ 创建问题文件: {filepath} ({len(problematic_content)} 字节)")
        return filepath
        
    except Exception as e:
        print(f"✗ 创建问题文件失败: {str(e)}")
        return None

def test_encoding_detection():
    """测试编码检测功能"""
    print("\n=== 编码检测测试 ===")
    
    # 模拟编码检测函数
    def detect_file_encoding_mock(filepath):
        """模拟编码检测"""
        import chardet
        
        try:
            with open(filepath, 'rb') as f:
                raw_data = f.read(8192)
            
            # 检查BOM
            if raw_data.startswith(b'\xef\xbb\xbf'):
                return 'utf-8-sig'
            elif raw_data.startswith(b'\xff\xfe'):
                return 'utf-16-le'
            elif raw_data.startswith(b'\xfe\xff'):
                return 'utf-16-be'
            
            # 使用chardet检测
            result = chardet.detect(raw_data)
            detected = result.get('encoding', 'utf-8') if result else 'utf-8'
            confidence = result.get('confidence', 0.0) if result else 0.0
            
            print(f"  chardet检测: {detected} (置信度: {confidence:.3f})")
            
            # 验证编码
            encodings_to_try = [detected, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
            
            for encoding in encodings_to_try:
                try:
                    with open(filepath, 'r', encoding=encoding) as f:
                        content = f.read(1000)
                        if content and len(content) > 10:
                            print(f"  ✓ 成功使用编码: {encoding}")
                            return encoding
                except UnicodeDecodeError:
                    print(f"  ✗ 编码失败: {encoding}")
                    continue
            
            return 'utf-8'
            
        except Exception as e:
            print(f"  ✗ 检测失败: {str(e)}")
            return 'utf-8'
    
    # 创建测试文件
    test_files = create_test_csv_files()
    
    # 测试每个文件
    for encoding, file_info in test_files.items():
        print(f"\n测试 {file_info['description']}:")
        detected = detect_file_encoding_mock(file_info['path'])
        print(f"  预期编码: {encoding}")
        print(f"  检测编码: {detected}")
        print(f"  检测正确: {'✓' if detected == encoding or (encoding == 'utf-8-sig' and detected == 'utf-8') else '✗'}")
    
    # 测试问题文件
    problematic_file = create_problematic_csv_file()
    if problematic_file:
        print(f"\n测试问题文件:")
        detected = detect_file_encoding_mock(problematic_file)
        print(f"  检测编码: {detected}")
        
        # 测试错误处理
        try:
            with open(problematic_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print("  ✗ UTF-8解码意外成功")
        except UnicodeDecodeError as e:
            print(f"  ✓ UTF-8解码失败（预期）: {str(e)}")
        
        # 测试错误处理模式
        try:
            with open(problematic_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            print(f"  ✓ 忽略错误模式成功，内容长度: {len(content)}")
        except Exception as e:
            print(f"  ✗ 忽略错误模式失败: {str(e)}")
    
    # 清理文件
    for file_info in test_files.values():
        try:
            os.unlink(file_info['path'])
        except:
            pass
    
    if problematic_file:
        try:
            os.unlink(problematic_file)
        except:
            pass

def test_csv_parsing_with_errors():
    """测试CSV解析的错误处理"""
    print("\n=== CSV解析错误处理测试 ===")
    
    def parse_csv_with_error_handling(filepath):
        """模拟带错误处理的CSV解析"""
        import csv
        
        # 尝试检测编码
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        
        for encoding in encodings_to_try:
            try:
                print(f"  尝试编码: {encoding}")
                
                with open(filepath, 'r', encoding=encoding, newline='') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    
                print(f"    ✓ 成功读取 {len(rows)} 行")
                return rows, encoding
                
            except UnicodeDecodeError as e:
                print(f"    ✗ 编码错误: {str(e)}")
                
                # 尝试错误处理模式
                try:
                    with open(filepath, 'r', encoding=encoding, errors='ignore', newline='') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                    
                    print(f"    ✓ 忽略错误模式成功，读取 {len(rows)} 行")
                    return rows, f"{encoding}(ignore)"
                    
                except Exception as e2:
                    print(f"    ✗ 忽略错误模式也失败: {str(e2)}")
                    continue
                    
            except Exception as e:
                print(f"    ✗ 其他错误: {str(e)}")
                continue
        
        print("  ✗ 所有编码都失败")
        return [], None
    
    # 创建测试文件
    test_files = create_test_csv_files()
    problematic_file = create_problematic_csv_file()
    
    # 测试正常文件
    for encoding, file_info in test_files.items():
        print(f"\n解析 {file_info['description']}:")
        rows, used_encoding = parse_csv_with_error_handling(file_info['path'])
        print(f"  使用编码: {used_encoding}")
        print(f"  解析行数: {len(rows)}")
        if rows:
            print(f"  标题行: {rows[0]}")
    
    # 测试问题文件
    if problematic_file:
        print(f"\n解析问题文件:")
        rows, used_encoding = parse_csv_with_error_handling(problematic_file)
        print(f"  使用编码: {used_encoding}")
        print(f"  解析行数: {len(rows)}")
        if rows:
            print(f"  标题行: {rows[0]}")
    
    # 清理文件
    for file_info in test_files.values():
        try:
            os.unlink(file_info['path'])
        except:
            pass
    
    if problematic_file:
        try:
            os.unlink(problematic_file)
        except:
            pass

def test_error_scenarios():
    """测试各种错误场景"""
    print("\n=== 错误场景测试 ===")
    
    scenarios = [
        {
            'name': '0xcf字节错误',
            'content': b'\xcf\xbb\xbf' + '测试,数据\n'.encode('gbk'),
            'expected_error': 'invalid continuation byte'
        },
        {
            'name': '混合编码',
            'content': '测试,数据\n'.encode('utf-8') + '中文,内容\n'.encode('gbk'),
            'expected_error': 'codec can\'t decode'
        },
        {
            'name': '截断文件',
            'content': '测试项目名称,HTTP负载\n"测试","GET /api'.encode('utf-8'),
            'expected_error': None  # 应该能正常处理
        }
    ]
    
    for scenario in scenarios:
        print(f"\n测试场景: {scenario['name']}")
        
        # 创建测试文件
        fd, filepath = tempfile.mkstemp(suffix='.csv', prefix='error_test_')
        try:
            with open(filepath, 'wb') as f:
                f.write(scenario['content'])
            
            # 测试UTF-8直接读取
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"  ✓ UTF-8直接读取成功")
            except UnicodeDecodeError as e:
                print(f"  ✗ UTF-8直接读取失败: {str(e)}")
                
                # 测试错误处理模式
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    print(f"  ✓ 忽略错误模式成功，内容长度: {len(content)}")
                except Exception as e2:
                    print(f"  ✗ 忽略错误模式失败: {str(e2)}")
                
                # 测试替换模式
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='replace') as f:
                        content = f.read()
                    print(f"  ✓ 替换模式成功，内容长度: {len(content)}")
                except Exception as e3:
                    print(f"  ✗ 替换模式失败: {str(e3)}")
            
        finally:
            os.close(fd)
            try:
                os.unlink(filepath)
            except:
                pass

if __name__ == '__main__':
    print("=== CSV编码处理修复测试 ===")
    test_encoding_detection()
    test_csv_parsing_with_errors()
    test_error_scenarios()
    print("\n=== 测试完成 ===")
