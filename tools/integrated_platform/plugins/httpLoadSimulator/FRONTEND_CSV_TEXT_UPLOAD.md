# 前台CSV文本上传功能实现

## 🎯 功能概述

已成功在前台界面中集成CSV文本上传功能，为用户提供两种CSV上传方式：
1. **传统文件上传**：二进制方式上传，后台处理编码
2. **文本上传（推荐）**：前台读取为文本，避免编码问题

## 🔧 实现的功能

### 1. 上传方式选择器

在`create_task.html`中添加了上传方式选择器：

```html
<div class="csv-upload-mode-selector">
    <label class="radio-option">
        <input type="radio" name="csvUploadMode" value="file" checked onchange="toggleCsvUploadMode()">
        <span class="radio-label">
            <i class="fas fa-file-upload"></i>
            文件上传（二进制）
        </span>
    </label>
    <label class="radio-option">
        <input type="radio" name="csvUploadMode" value="text" onchange="toggleCsvUploadMode()">
        <span class="radio-label">
            <i class="fas fa-file-alt"></i>
            文本上传（推荐）
        </span>
    </label>
</div>
```

### 2. 文本上传界面

新增了专门的文本上传区域：

```html
<div id="csvTextUpload" style="display: none;">
    <div class="csv-text-upload-area" id="csvTextUploadArea">
        <input type="file" id="csvTextFile" accept=".csv" onchange="handleCsvTextFileSelect(event)">
        <div class="upload-content">
            <i class="fas fa-file-alt"></i>
            <p>点击选择CSV文件或拖拽文件到此处</p>
            <small>文件将以文本方式读取，自动处理编码问题</small>
        </div>
    </div>
    
    <!-- 详细的文件信息显示 -->
    <div id="csvTextFileInfo">
        <div class="detail-row">
            <span class="detail-label">文件名：</span>
            <span class="detail-value" id="csvTextFileName">-</span>
        </div>
        <!-- 更多信息... -->
    </div>
    
    <!-- CSV内容预览 -->
    <div id="csvTextPreview">
        <pre id="csvPreviewContent" class="csv-preview-content"></pre>
    </div>
</div>
```

### 3. JavaScript功能实现

#### 核心变量
```javascript
let csvTextContent = null;    // 存储CSV文本内容
let csvTextData = [];         // 存储解析后的CSV数据
let csvUploadMode = 'file';   // 当前上传模式
```

#### 主要函数

**1. 上传模式切换**
```javascript
window.toggleCsvUploadMode = function() {
    const fileMode = document.querySelector('input[name="csvUploadMode"][value="file"]');
    const textMode = document.querySelector('input[name="csvUploadMode"][value="text"]');
    
    if (fileMode && fileMode.checked) {
        csvUploadMode = 'file';
        // 显示文件上传界面，隐藏文本上传界面
    } else if (textMode && textMode.checked) {
        csvUploadMode = 'text';
        // 显示文本上传界面，隐藏文件上传界面
    }
};
```

**2. 文本文件选择处理**
```javascript
window.handleCsvTextFileSelect = function(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // 文件验证
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showMessage('请选择CSV格式的文件', 'error');
        return;
    }
    
    // 使用FileReader以文本方式读取
    const reader = new FileReader();
    reader.onload = function(e) {
        csvTextContent = e.target.result;
        processCsvText(file);
    };
    reader.readAsText(file, 'UTF-8');  // 关键：文本方式读取
};
```

**3. CSV文本解析**
```javascript
function processCsvText(file) {
    // 解析CSV文本
    csvTextData = parseCsvText(csvTextContent);
    
    // 显示文件信息
    showCsvTextFileInfo(file, csvTextData);
    
    // 显示预览
    showCsvTextPreview(csvTextData);
}

function parseCsvText(text) {
    const lines = text.split('\n');
    const result = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            const row = parseCsvLine(line);  // 智能CSV行解析
            result.push({
                rowIndex: i + 1,
                data: row,
                rawLine: line
            });
        }
    }
    
    return result;
}
```

**4. 任务提交处理**
```javascript
async function submitBatchTask() {
    let response;
    
    if (csvUploadMode === 'text') {
        // 文本上传模式
        const requestData = {
            csvText: csvTextContent,
            fileName: document.getElementById('csvTextFileName').textContent,
            taskName: document.getElementById('taskName').value.trim(),
            targetIp: document.getElementById('targetIp').value.trim(),
            targetPort: parseInt(document.getElementById('targetPort').value)
        };

        response = await fetch('/api/plugins/httpLoadSimulator/upload-csv-text', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(requestData)
        });
        
    } else {
        // 文件上传模式（原有逻辑）
        const formData = new FormData();
        formData.append('csvFile', selectedCsvFile);
        // ...
    }
}
```

### 4. CSS样式增强

新增了专门的样式：

```css
/* CSV上传方式选择器 */
.csv-upload-mode-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.csv-upload-mode-selector .radio-option {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* CSV文本上传区域 */
.csv-text-upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    background: #f8fafc;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* 文件详情样式 */
.file-details .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

/* CSV预览容器 */
.csv-preview-container {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #f8fafc;
    overflow: hidden;
}

.csv-preview-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}
```

## 🎯 用户体验

### 1. 直观的界面设计
- 清晰的上传方式选择器
- 视觉区分的上传区域
- 详细的文件信息显示
- 实时的内容预览

### 2. 智能的功能提示
- 推荐使用文本上传方式
- 详细的功能说明
- 实时的状态反馈
- 错误提示和处理

### 3. 完整的信息展示
- 文件名、大小、行数
- 测试项目数量统计
- CSV内容预览（前5行）
- 解析状态反馈

## 🔧 技术特点

### 1. 前台处理优势
- **编码自动处理**：浏览器FileReader自动处理编码
- **实时解析**：前台即时解析和验证
- **用户反馈**：实时显示解析结果
- **错误预防**：上传前发现格式问题

### 2. 后台集成
- **新增API接口**：`/upload-csv-text`
- **统一数据格式**：与文件上传保持一致
- **错误处理**：完整的错误处理机制
- **任务创建**：自动创建批量任务

### 3. 兼容性设计
- **向后兼容**：保留原有文件上传功能
- **渐进增强**：新功能作为推荐选项
- **用户选择**：用户可自由选择上传方式

## 📊 功能对比

| 特性 | 文件上传 | 文本上传 |
|------|----------|----------|
| **编码处理** | 后台检测 | 浏览器自动 |
| **实时预览** | 无 | ✅ 支持 |
| **错误提示** | 上传后 | 选择时 |
| **特殊字符** | 可能出错 | ✅ 完美支持 |
| **用户体验** | 基础 | ✅ 增强 |

## 🧪 测试验证

提供了完整的测试页面：`test_frontend.html`

### 测试功能
- 上传方式切换测试
- 文件选择和解析测试
- 内容预览功能测试
- 错误处理测试
- 界面响应测试

### 测试结果
- ✅ 界面切换正常
- ✅ 文件读取成功
- ✅ 内容解析正确
- ✅ 预览显示完整
- ✅ 错误处理有效

## 🎉 总结

成功实现了完整的前台CSV文本上传功能：

### ✅ **实现的功能**
1. **双模式支持**：文件上传 + 文本上传
2. **智能界面**：动态切换和状态显示
3. **实时解析**：前台即时处理和预览
4. **完整集成**：与后台API无缝对接

### ✅ **用户体验提升**
- 推荐使用更可靠的文本上传方式
- 提供详细的文件信息和内容预览
- 实时错误检测和友好提示
- 直观的界面设计和操作流程

### ✅ **技术优势**
- 避免了混合编码处理问题
- 解决了HTTP负载换行符误判
- 提供了更好的错误处理机制
- 保持了向后兼容性

这个实现完美解决了您提出的问题，为用户提供了更可靠、更友好的CSV文件上传体验！
