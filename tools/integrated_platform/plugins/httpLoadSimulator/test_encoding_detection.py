#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的编码检测功能
"""

import chardet

def test_chardet_issues():
    """测试chardet的误判问题"""
    print("=== chardet误判问题分析 ===")
    print()
    
    # 测试用例：容易被误判的中文字节序列
    test_cases = [
        (b'\xcf\xee\xc4\xbf,\xb8\xba\xd4\xd8', '项目,负载'),
        (b'\xcf\xee\xc4\xbf', '项目'),
        (b'\xb8\xba\xd4\xd8', '负载'),
        (b'\xcd\xa8\xd3\xc3', '通用'),
        (b'\xce\xc4\xbc\xfe', '文件'),
    ]
    
    for test_bytes, expected_chinese in test_cases:
        print(f"测试序列: {test_bytes.hex().upper()}")
        print(f"期望中文: {expected_chinese}")
        
        # chardet检测
        result = chardet.detect(test_bytes)
        print(f"chardet检测: {result['encoding']} (置信度: {result['confidence']:.3f})")
        
        # 用检测到的编码解码
        try:
            decoded_detected = test_bytes.decode(result['encoding'])
            print(f"检测编码解码: \"{decoded_detected}\"")
        except:
            print(f"检测编码解码: 失败")
        
        # 用GBK解码
        try:
            decoded_gbk = test_bytes.decode('gbk')
            print(f"GBK解码: \"{decoded_gbk}\"")
            print(f"GBK正确性: {'✓' if decoded_gbk == expected_chinese else '✗'}")
        except:
            print(f"GBK解码: 失败")
        
        print()

def smart_encoding_detection(data: bytes) -> str:
    """改进的编码检测函数"""
    # 1. chardet初步检测
    chardet_result = chardet.detect(data)
    detected_encoding = chardet_result['encoding'] if chardet_result['encoding'] else 'utf-8'
    confidence = chardet_result['confidence']
    
    print(f"  chardet: {detected_encoding} (置信度: {confidence:.3f})")
    
    # 2. 高置信度且非容易误判的编码，直接使用
    if confidence > 0.8 and detected_encoding not in ['windows-1251', 'windows-1252', 'iso-8859-1']:
        print(f"  -> 高置信度，使用 {detected_encoding}")
        return detected_encoding
    
    # 3. 低置信度或容易误判，测试中文编码
    if confidence < 0.7 or detected_encoding in ['windows-1251', 'windows-1252', 'iso-8859-1']:
        print(f"  -> 可能误判，测试中文编码...")
        
        chinese_encodings = ['gbk', 'gb2312', 'utf-8']
        best_encoding = detected_encoding
        best_score = 0
        
        for encoding in chinese_encodings:
            try:
                decoded = data.decode(encoding)
                
                # 计算中文字符比例
                chinese_count = sum(1 for c in decoded if is_chinese_char(c))
                printable_count = len([c for c in decoded if c.isprintable() or ord(c) > 127])
                
                if printable_count > 0:
                    chinese_ratio = chinese_count / printable_count
                    print(f"    {encoding}: 中文比例 {chinese_ratio:.2f}, 内容: \"{decoded}\"")
                    
                    # 评分：中文比例 + 可读性
                    score = chinese_ratio
                    if chinese_ratio > 0.5:  # 如果中文比例高，额外加分
                        score += 0.3
                    
                    if score > best_score:
                        best_score = score
                        best_encoding = encoding
                        
            except UnicodeDecodeError:
                print(f"    {encoding}: 解码失败")
        
        if best_score > 0.3:
            print(f"  -> 推荐使用 {best_encoding} (评分: {best_score:.2f})")
            return best_encoding
    
    print(f"  -> 使用原始检测结果 {detected_encoding}")
    return detected_encoding

def is_chinese_char(char: str) -> bool:
    """判断是否为中文字符"""
    code = ord(char)
    return (0x4e00 <= code <= 0x9fff) or (0x3400 <= code <= 0x4dbf) or (0x20000 <= code <= 0x2a6df)

def test_smart_detection():
    """测试改进的编码检测"""
    print("=== 测试改进的编码检测 ===")
    print()
    
    test_cases = [
        (b'\xcf\xee\xc4\xbf,\xb8\xba\xd4\xd8', '项目,负载'),
        (b'\xcf\xee\xc4\xbf', '项目'),
        (b'\xcd\xa8\xd3\xc3_\xce\xc4\xbc\xfe\xc9\xcf\xb4\xab', '通用_文件上传'),
        (b'Hello World', 'Hello World'),  # 英文测试
        (b'\xe4\xb8\xad\xe6\x96\x87', '中文'),  # UTF-8中文
    ]
    
    for test_bytes, expected in test_cases:
        print(f"测试: {test_bytes.hex().upper()}")
        print(f"期望: {expected}")
        
        # 使用改进的检测
        detected_encoding = smart_encoding_detection(test_bytes)
        
        # 验证结果
        try:
            decoded = test_bytes.decode(detected_encoding)
            print(f"最终结果: \"{decoded}\"")
            print(f"正确性: {'✓' if decoded == expected else '✗'}")
        except Exception as e:
            print(f"解码失败: {e}")
        
        print("-" * 50)

def test_mixed_content():
    """测试混合内容的编码检测"""
    print("=== 测试混合内容编码检测 ===")
    print()
    
    # 模拟CSV行：中文项目名 + ASCII HTTP负载
    mixed_content = b'\xcf\xee\xc4\xbf\xb2\xe2\xca\xd4,"GET /api HTTP/1.1\\r\\nHost: example.com\\r\\n\\r\\n"'
    
    print(f"混合内容: {mixed_content.hex().upper()}")
    print("内容分析:")
    print("  前半部分: 中文 (GBK编码)")
    print("  后半部分: ASCII HTTP负载")
    print()
    
    # chardet检测
    result = chardet.detect(mixed_content)
    print(f"chardet检测: {result['encoding']} (置信度: {result['confidence']:.3f})")
    
    # 改进检测
    print("改进检测:")
    smart_encoding = smart_encoding_detection(mixed_content)
    
    # 验证解码
    try:
        decoded = mixed_content.decode(smart_encoding)
        print(f"解码结果: {decoded}")
    except Exception as e:
        print(f"解码失败: {e}")

def test_confidence_threshold():
    """测试不同置信度阈值的效果"""
    print("=== 测试置信度阈值效果 ===")
    print()
    
    test_data = b'\xcf\xee\xc4\xbf,\xb8\xba\xd4\xd8'
    result = chardet.detect(test_data)
    
    print(f"测试数据: {test_data.hex().upper()}")
    print(f"chardet结果: {result}")
    print()
    
    thresholds = [0.5, 0.6, 0.7, 0.8, 0.9]
    
    for threshold in thresholds:
        print(f"置信度阈值 {threshold}:")
        if result['confidence'] >= threshold:
            print(f"  使用chardet结果: {result['encoding']}")
        else:
            print(f"  置信度不足，尝试中文编码...")
            # 这里会尝试GBK等编码
            try:
                gbk_decoded = test_data.decode('gbk')
                print(f"  GBK解码成功: {gbk_decoded}")
            except:
                print(f"  GBK解码失败")
        print()

if __name__ == '__main__':
    test_chardet_issues()
    test_smart_detection()
    test_mixed_content()
    test_confidence_threshold()
