#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV文件处理功能
"""

import os
import sys
import tempfile
import csv
from io import BytesIO

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def create_test_csv_content():
    """创建测试CSV内容，包含换行符和特殊字符"""
    test_data = [
        ["测试项目名称", "HTTP负载"],
        ["简单GET请求", "GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: TestAgent/1.0\r\n\r\n"],
        ["POST请求带JSON", "POST /api/data HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\nContent-Length: 25\r\n\r\n{\"name\":\"test\",\"value\":123}"],
        ["包含中文的请求", "GET /中文路径/测试 HTTP/1.1\r\nHost: 测试.com\r\nAccept: text/html\r\n\r\n"],
        ["多行HTTP头", "GET /complex HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Mozilla/5.0\r\nAccept: */*\r\nConnection: keep-alive\r\nAuthorization: Bearer token123\r\n\r\n"]
    ]
    return test_data

def create_test_csv_file(content, encoding='utf-8'):
    """创建测试CSV文件"""
    output = BytesIO()
    
    # 将内容写入字节流
    csv_content = ""
    for row in content:
        # 使用CSV格式，确保包含换行符的字段被正确引用
        csv_row = []
        for field in row:
            # 如果字段包含特殊字符，需要用引号包围
            if '\r' in field or '\n' in field or ',' in field or '"' in field:
                # 转义引号并用引号包围
                escaped_field = field.replace('"', '""')
                csv_row.append(f'"{escaped_field}"')
            else:
                csv_row.append(field)
        csv_content += ','.join(csv_row) + '\n'
    
    # 编码为字节
    output.write(csv_content.encode(encoding))
    output.seek(0)
    return output

def test_csv_processing():
    """测试CSV处理功能"""
    print("开始测试CSV文件处理功能...")
    
    # 创建测试数据
    test_content = create_test_csv_content()
    print(f"创建了 {len(test_content)-1} 个测试项目")
    
    # 测试不同编码
    encodings_to_test = ['utf-8', 'gbk', 'utf-8-sig']
    
    for encoding in encodings_to_test:
        print(f"\n测试编码: {encoding}")
        
        try:
            # 创建测试CSV文件
            csv_file_obj = create_test_csv_file(test_content, encoding)
            
            # 模拟Flask的FileStorage对象
            class MockFileStorage:
                def __init__(self, data, filename):
                    self.data = data
                    self.filename = filename
                    self.position = 0
                
                def read(self):
                    self.data.seek(self.position)
                    content = self.data.read()
                    self.position = self.data.tell()
                    return content
                
                def seek(self, position):
                    self.position = position
                    self.data.seek(position)
            
            mock_file = MockFileStorage(csv_file_obj, f'test_{encoding}.csv')
            
            # 创建临时目录和文件路径
            with tempfile.TemporaryDirectory() as temp_dir:
                csv_file_path = os.path.join(temp_dir, f'processed_{encoding}.csv')
                
                # 这里需要导入插件类来测试
                # 由于无法直接导入，我们创建一个简化的测试函数
                result = process_csv_file_test(mock_file, csv_file_path)
                
                if result:
                    print(f"  ✓ 编码 {encoding} 处理成功，解析了 {len(result)} 个测试项目")
                    
                    # 验证保存的文件
                    if os.path.exists(csv_file_path):
                        print(f"  ✓ CSV文件已保存: {csv_file_path}")
                        
                        # 读取保存的文件验证内容
                        with open(csv_file_path, 'r', encoding='utf-8-sig') as f:
                            saved_content = f.read()
                            if 'GET /api/test HTTP/1.1' in saved_content:
                                print("  ✓ HTTP负载内容保存正确")
                            else:
                                print("  ✗ HTTP负载内容保存有误")
                    else:
                        print("  ✗ CSV文件未保存")
                else:
                    print(f"  ✗ 编码 {encoding} 处理失败")
                    
        except Exception as e:
            print(f"  ✗ 编码 {encoding} 测试出错: {str(e)}")

def process_csv_file_test(csv_file, csv_file_path):
    """简化的CSV处理测试函数"""
    try:
        import csv
        import chardet
        import io
        
        # 读取上传文件的原始数据
        csv_file.seek(0)
        raw_data = csv_file.read()
        
        # 检测文件编码
        encoding_result = chardet.detect(raw_data)
        encoding = encoding_result['encoding'] if encoding_result['encoding'] else 'utf-8'
        
        # 尝试多种编码方式解析文件内容
        encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        csv_data = []
        
        for enc in encodings_to_try:
            try:
                # 将字节数据解码为字符串
                if isinstance(raw_data, bytes):
                    text_data = raw_data.decode(enc)
                else:
                    text_data = raw_data
                
                # 使用StringIO创建文件对象
                string_io = io.StringIO(text_data)
                
                # 尝试检测CSV方言
                sample = text_data[:1024]
                sniffer = csv.Sniffer()
                try:
                    dialect = sniffer.sniff(sample)
                except:
                    dialect = csv.excel
                
                # 重置StringIO位置
                string_io.seek(0)
                reader = csv.reader(string_io, dialect)
                
                temp_csv_data = []
                for row_index, row in enumerate(reader, 1):
                    if row_index == 1:
                        # 第一行是标题行
                        if len(row) < 2:
                            raise ValueError("CSV文件必须至少包含两列")
                        continue
                    
                    if len(row) >= 2:
                        test_name = row[0].strip()
                        http_payload = row[1].strip()
                        
                        if test_name and http_payload:
                            temp_csv_data.append({
                                'testName': test_name,
                                'httpPayload': http_payload,
                                'rowIndex': row_index
                            })
                
                csv_data = temp_csv_data
                break
                
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                continue
        
        if not csv_data:
            return []
        
        # 以标准格式保存CSV文件
        save_standard_csv_file_test(csv_data, csv_file_path)
        
        return csv_data
        
    except Exception as e:
        print(f"处理CSV文件失败: {str(e)}")
        return []

def save_standard_csv_file_test(csv_data, csv_file_path):
    """保存标准格式CSV文件的测试函数"""
    try:
        import csv
        
        # 确保目录存在
        os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)
        
        # 以UTF-8编码保存
        with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['测试项目名称', 'HTTP负载']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
            
            # 写入标题行
            writer.writeheader()
            
            # 写入数据行
            for item in csv_data:
                writer.writerow({
                    '测试项目名称': item['testName'],
                    'HTTP负载': item['httpPayload']
                })
        
        return True
        
    except Exception as e:
        print(f"保存CSV文件失败: {str(e)}")
        return False

if __name__ == '__main__':
    test_csv_processing()
