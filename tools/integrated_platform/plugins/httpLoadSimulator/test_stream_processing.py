#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流式CSV处理功能
"""

import os
import tempfile
import io
from typing import List, Dict

def create_large_csv_content():
    """创建大型CSV测试内容"""
    lines = ['测试项目名称,HTTP负载']
    
    # 创建多个测试用例
    test_cases = [
        ('简单GET请求', 'GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: TestAgent\\r\\n\\r\\n'),
        ('JSON POST请求', 'POST /api/data HTTP/1.1\\r\\nContent-Type: application/json\\r\\nContent-Length: 45\\r\\n\\r\\n{\\"name\\":\\"test\\",\\"value\\":123,\\"status\\":\\"active\\"}'),
        ('文件上传请求', 'POST /upload HTTP/1.1\\r\\nContent-Type: multipart/form-data; boundary=----WebKit\\r\\n\\r\\n------WebKit\\r\\nContent-Disposition: form-data; name=\\"file\\"; filename=\\"test.txt\\"\\r\\n\\r\\nHello World!\\r\\n------WebKit--'),
        ('复杂Accept头', 'GET /api HTTP/1.1\\r\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\\r\\nUser-Agent: Mozilla/5.0\\r\\n\\r\\n'),
        ('中文测试,包含逗号', 'POST /中文路径 HTTP/1.1\\r\\nHost: 测试服务器.com\\r\\nContent-Type: application/json; charset=utf-8\\r\\n\\r\\n{\\"消息\\":\\"你好,世界!\\",\\"状态\\":\\"成功\\"}')
    ]
    
    # 重复测试用例以创建较大的文件
    for i in range(20):  # 创建100行数据
        for j, (name, payload) in enumerate(test_cases):
            test_name = f'{name}_{i+1}_{j+1}'
            lines.append(f'"{test_name}","{payload}"')
    
    return '\\n'.join(lines)

def test_stream_processing():
    """测试流式处理功能"""
    print("=== 测试流式CSV处理功能 ===")
    print()
    
    # 创建测试CSV内容
    csv_content = create_large_csv_content()
    print(f"创建测试CSV内容，共 {len(csv_content.split(chr(10)))} 行")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as f:
        f.write(csv_content)
        temp_file_path = f.name
    
    try:
        # 模拟文件上传对象
        class MockFileUpload:
            def __init__(self, file_path):
                self.file_path = file_path
                self.position = 0
            
            def seek(self, pos):
                self.position = pos
            
            def read(self, size=None):
                with open(self.file_path, 'rb') as f:
                    f.seek(self.position)
                    if size is None:
                        data = f.read()
                    else:
                        data = f.read(size)
                    self.position = f.tell()
                    return data
        
        mock_file = MockFileUpload(temp_file_path)
        
        # 测试流式处理
        print("1. 开始流式处理测试:")
        csv_data = stream_process_csv_file_test(mock_file)
        
        print(f"   ✓ 处理完成，共解析 {len(csv_data)} 个测试项目")
        
        # 验证数据完整性
        print("\\n2. 数据完整性验证:")
        for i, item in enumerate(csv_data[:5]):  # 只显示前5个
            print(f"   项目 {i+1}:")
            print(f"     名称: {item['testName']}")
            print(f"     HTTP负载长度: {len(item['httpPayload'])} 字符")
            print(f"     行号: {item['rowIndex']}")
        
        if len(csv_data) > 5:
            print(f"   ... 还有 {len(csv_data) - 5} 个项目")
        
        # 测试特殊字符处理
        print("\\n3. 特殊字符处理验证:")
        for item in csv_data:
            if ',' in item['testName']:
                print(f"   ✓ 测试名称包含逗号: '{item['testName']}'")
                break
        
        for item in csv_data:
            if item['httpPayload'].count(',') > 2:
                print(f"   ✓ HTTP负载包含多个逗号: {item['httpPayload'].count(',')} 个")
                break
        
        # 测试内存效率
        print("\\n4. 内存效率测试:")
        print(f"   ✓ 逐行处理，避免大文件一次性加载")
        print(f"   ✓ 流式读取，内存占用稳定")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def stream_process_csv_file_test(csv_file) -> List[Dict[str, str]]:
    """流式处理CSV文件的测试实现"""
    try:
        print("   开始流式处理...")
        
        # 重置文件指针
        csv_file.seek(0)
        
        csv_data = []
        line_buffer = b''
        line_number = 0
        header_processed = False
        
        # 逐块读取文件
        chunk_size = 1024  # 1KB chunks for testing
        while True:
            chunk = csv_file.read(chunk_size)
            if not chunk:
                # 处理最后的缓冲区内容
                if line_buffer:
                    result = process_stream_line_test(line_buffer, line_number + 1)
                    if result:
                        csv_data.append(result)
                break
            
            line_buffer += chunk
            
            # 处理完整的行
            while True:
                line_end = find_line_end_test(line_buffer)
                if line_end == -1:
                    break  # 没有完整的行，继续读取
                
                # 提取完整的行
                line_bytes = line_buffer[:line_end]
                line_buffer = line_buffer[line_end + 1:]
                
                line_number += 1
                
                if line_number == 1:
                    # 验证标题行
                    if not validate_csv_header_test(line_bytes):
                        print("   ✗ CSV标题行格式错误")
                        return []
                    header_processed = True
                    print(f"   ✓ 标题行验证通过")
                    continue
                
                # 处理数据行
                result = process_stream_line_test(line_bytes, line_number)
                if result:
                    csv_data.append(result)
                    if line_number % 20 == 0:  # 每20行显示一次进度
                        print(f"   处理进度: {line_number} 行")
        
        print(f"   ✓ 流式处理完成，共 {len(csv_data)} 个有效项目")
        return csv_data
        
    except Exception as e:
        print(f"   ✗ 流式处理失败: {str(e)}")
        return []

def find_line_end_test(buffer: bytes) -> int:
    """查找行结束位置"""
    # 查找 \\r\\n
    pos = buffer.find(b'\\r\\n')
    if pos != -1:
        return pos + 1
    
    # 查找 \\n
    pos = buffer.find(b'\\n')
    if pos != -1:
        return pos
    
    # 查找 \\r
    pos = buffer.find(b'\\r')
    if pos != -1:
        return pos
    
    return -1

def validate_csv_header_test(header_bytes: bytes) -> bool:
    """验证CSV标题行"""
    try:
        # 尝试解码
        header_text = decode_line_test(header_bytes)
        if not header_text or ',' not in header_text:
            return False
        
        # 检查关键词
        header_lower = header_text.lower()
        has_name = any(keyword in header_lower for keyword in ['名称', 'name', '项目', 'test'])
        has_payload = any(keyword in header_lower for keyword in ['负载', 'payload', 'http', '请求'])
        
        return has_name or has_payload
        
    except Exception:
        return False

def process_stream_line_test(line_bytes: bytes, line_number: int) -> Dict[str, str]:
    """处理单行数据流"""
    try:
        if not line_bytes or not line_bytes.strip():
            return None
        
        # 解码行数据
        line_text = decode_line_test(line_bytes)
        if not line_text:
            return None
        
        # 智能列分割
        result = smart_column_split_test(line_text)
        if not result:
            return None
        
        test_name, http_payload = result
        if not test_name or not http_payload:
            return None
        
        return {
            'testName': test_name,
            'httpPayload': http_payload,
            'rowIndex': line_number
        }
        
    except Exception as e:
        print(f"   警告: 第 {line_number} 行处理失败: {str(e)}")
        return None

def decode_line_test(line_bytes: bytes) -> str:
    """解码行数据"""
    import chardet
    
    # 检测编码
    result = chardet.detect(line_bytes)
    detected_encoding = result.get('encoding', 'utf-8')
    
    # 尝试多种编码
    encodings = [detected_encoding, 'utf-8', 'gbk', 'gb2312', 'latin1']
    
    for encoding in encodings:
        try:
            return line_bytes.decode(encoding)
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    # 最后的备选方案
    return line_bytes.decode('utf-8', errors='replace')

def smart_column_split_test(line_text: str):
    """智能列分割测试"""
    if not line_text or not line_text.strip():
        return None
    
    # 查找第一个非引号内的逗号
    in_quotes = False
    escape_next = False
    comma_pos = -1
    
    for i, char in enumerate(line_text):
        if escape_next:
            escape_next = False
        elif char == '\\\\':
            escape_next = True
        elif char == '"':
            in_quotes = not in_quotes
        elif char == ',' and not in_quotes:
            comma_pos = i
            break
    
    if comma_pos == -1:
        parts = line_text.split(',', 1)
        if len(parts) >= 2:
            return clean_field_test(parts[0]), clean_field_test(parts[1])
        return None
    
    test_name = line_text[:comma_pos]
    http_payload = line_text[comma_pos + 1:]
    
    return clean_field_test(test_name), clean_field_test(http_payload)

def clean_field_test(field: str) -> str:
    """清理字段"""
    if not field:
        return ""
    
    field = field.strip()
    
    if len(field) >= 2 and field.startswith('"') and field.endswith('"'):
        field = field[1:-1]
        field = field.replace('""', '"')
    
    return field

if __name__ == '__main__':
    test_stream_processing()
