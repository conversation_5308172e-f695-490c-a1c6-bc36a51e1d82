#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的混合编码CSV文件处理
"""

import os
import tempfile
import csv

def create_realistic_mixed_csv():
    """创建更真实的混合编码CSV文件"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.csv') as f:
        # 写入UTF-8编码的标题行
        header = "测试项目名称,HTTP负载\n"
        f.write(header.encode('utf-8'))
        
        # 第一行：GBK编码的中文名称 + ASCII HTTP负载
        test_name_1 = "项目,负载测试"
        http_payload_1 = "GET /api/test HTTP/1.1\\r\\nHost: example.com\\r\\nUser-Agent: TestAgent\\r\\n\\r\\n"
        
        # 构造正确的CSV行
        csv_line_1 = f'"{test_name_1}","{http_payload_1}"\n'
        f.write(csv_line_1.encode('gbk'))
        
        # 第二行：更复杂的混合编码
        test_name_2 = "文件上传_特征隐藏webshell_绕过检测"
        http_payload_2 = "POST /upload.php HTTP/1.1\\r\\nHost: target.com\\r\\nContent-Type: multipart/form-data\\r\\n\\r\\n--boundary\\r\\nContent-Disposition: form-data; name=\"file\"; filename=\"test.php\"\\r\\n\\r\\n<?php echo \"hello\"; ?>\\r\\n--boundary--"
        
        csv_line_2 = f'"{test_name_2}","{http_payload_2}"\n'
        f.write(csv_line_2.encode('gbk'))
        
        temp_file_path = f.name
    
    return temp_file_path

def test_realistic_processing():
    """测试真实的CSV处理"""
    print("=== 测试真实混合编码CSV处理 ===")
    print()
    
    # 创建测试文件
    csv_file_path = create_realistic_mixed_csv()
    print(f"创建测试CSV文件: {csv_file_path}")
    
    try:
        # 读取原始字节数据
        with open(csv_file_path, 'rb') as f:
            raw_data = f.read()
        
        print(f"文件大小: {len(raw_data)} 字节")
        print(f"十六进制预览: {raw_data[:100].hex().upper()}...")
        print()
        
        # 测试我们的处理逻辑
        print("1. 测试行分割:")
        lines = split_lines(raw_data)
        print(f"   分割得到 {len(lines)} 行")
        
        for i, line in enumerate(lines):
            print(f"   行 {i+1}: {len(line)} 字节")
        print()
        
        print("2. 测试逐行解码:")
        decoded_lines = []
        for i, line in enumerate(lines):
            decoded = decode_line_smart(line)
            decoded_lines.append(decoded)
            print(f"   行 {i+1}: {decoded[:80]}...")
        print()
        
        print("3. 测试CSV解析:")
        csv_data = parse_csv_lines(decoded_lines)
        print(f"   解析得到 {len(csv_data)} 个测试项目")
        
        for i, item in enumerate(csv_data):
            print(f"   项目 {i+1}:")
            print(f"     名称: {item['testName']}")
            print(f"     HTTP负载: {len(item['httpPayload'])} 字符")
            print(f"     负载预览: {item['httpPayload'][:100]}...")
        
    finally:
        # 清理临时文件
        if os.path.exists(csv_file_path):
            os.unlink(csv_file_path)

def split_lines(raw_data: bytes):
    """分割行"""
    line_separators = [b'\\r\\n', b'\\n', b'\\r']
    
    for separator in line_separators:
        if separator in raw_data:
            lines = raw_data.split(separator)
            lines = [line for line in lines if line.strip()]
            if len(lines) >= 2:
                return lines
    
    return [raw_data] if raw_data.strip() else []

def decode_line_smart(line_bytes: bytes) -> str:
    """智能解码行数据"""
    import chardet
    
    if not line_bytes:
        return ""
    
    # 检测编码
    result = chardet.detect(line_bytes)
    detected_encoding = result.get('encoding', 'utf-8')
    confidence = result.get('confidence', 0)
    
    # 定义编码尝试顺序
    encodings_to_try = []
    
    # 如果检测置信度高，优先使用检测到的编码
    if confidence > 0.7:
        encodings_to_try.append(detected_encoding)
    
    # 添加常用编码
    common_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
    for enc in common_encodings:
        if enc not in encodings_to_try:
            encodings_to_try.append(enc)
    
    # 尝试解码
    for encoding in encodings_to_try:
        try:
            decoded = line_bytes.decode(encoding)
            # 验证解码结果
            if is_valid_csv_line(decoded):
                return decoded
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    # 最后的备选方案
    try:
        return line_bytes.decode('utf-8', errors='replace')
    except:
        return line_bytes.decode('latin1', errors='replace')

def is_valid_csv_line(text: str) -> bool:
    """验证是否是有效的CSV行"""
    if not text or not text.strip():
        return False
    
    # 检查是否包含CSV特征
    csv_indicators = [',', '"', '\\r', '\\n', 'HTTP', 'GET', 'POST']
    return any(indicator in text for indicator in csv_indicators)

def parse_csv_lines(lines):
    """解析CSV行"""
    import csv
    import io
    
    csv_data = []
    
    for i, line in enumerate(lines):
        if i == 0:  # 跳过标题行
            continue
        
        if not line.strip():
            continue
        
        # 尝试CSV解析
        try:
            string_io = io.StringIO(line)
            reader = csv.reader(string_io)
            row = next(reader)
            
            if len(row) >= 2:
                test_name = row[0].strip()
                http_payload = row[1].strip()
                
                if test_name and http_payload:
                    csv_data.append({
                        'testName': test_name,
                        'httpPayload': http_payload,
                        'rowIndex': i + 1
                    })
        except Exception as e:
            print(f"   警告: 第 {i+1} 行解析失败: {str(e)}")
            # 尝试简单分割
            try:
                if '","' in line:
                    parts = line.split('","', 1)
                    if len(parts) == 2:
                        test_name = parts[0].strip().strip('"')
                        http_payload = parts[1].strip().strip('"')
                        if test_name and http_payload:
                            csv_data.append({
                                'testName': test_name,
                                'httpPayload': http_payload,
                                'rowIndex': i + 1
                            })
                            print(f"   使用简单分割成功解析第 {i+1} 行")
            except Exception as e2:
                print(f"   简单分割也失败: {str(e2)}")
    
    return csv_data

if __name__ == '__main__':
    test_realistic_processing()
