#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV解析修复 - 验证引号内换行符的正确处理
"""

def test_csv_parsing_scenarios():
    """测试各种CSV解析场景"""
    print("=== CSV解析修复测试 ===")
    print()
    
    # 测试场景1：包含HTTP负载的正常CSV
    test_case_1 = '''测试项目名称,HTTP负载
"GET请求测试","GET /api/test HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Mozilla/5.0\r\n\r\n"
"POST请求测试","POST /api/data HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\nContent-Length: 25\r\n\r\n{""key"": ""value""}"'''
    
    print("测试场景1：包含HTTP负载的正常CSV")
    print("原始CSV内容:")
    print(repr(test_case_1))
    print()
    
    # 模拟旧的错误解析方式
    print("旧的错误解析方式 (简单split):")
    old_lines = test_case_1.split('\n')
    print(f"分割出 {len(old_lines)} 行:")
    for i, line in enumerate(old_lines, 1):
        print(f"  行{i}: {repr(line)}")
    print("❌ 问题：HTTP负载被错误分割成多行")
    print()
    
    # 模拟新的正确解析方式
    print("新的正确解析方式 (考虑引号):")
    correct_rows = parse_csv_correctly(test_case_1)
    print(f"解析出 {len(correct_rows)} 个CSV行:")
    for i, row in enumerate(correct_rows, 1):
        print(f"  行{i}: {row['data']}")
        if len(row['data']) >= 2:
            print(f"    测试名称: {row['data'][0]}")
            print(f"    HTTP负载: {repr(row['data'][1])}")
    print("✅ 正确：HTTP负载完整保持")
    print()
    
    # 测试场景2：复杂的多行HTTP负载
    test_case_2 = '''测试项目名称,HTTP负载
"复杂POST请求","POST /upload HTTP/1.1\r\nHost: example.com\r\nContent-Type: multipart/form-data; boundary=----WebKit\r\nContent-Length: 234\r\n\r\n------WebKit\r\nContent-Disposition: form-data; name=""file""; filename=""test.txt""\r\nContent-Type: text/plain\r\n\r\nHello World!\r\n------WebKit--"
"JSON请求","POST /api/json HTTP/1.1\r\nHost: api.example.com\r\nContent-Type: application/json\r\nAuthorization: Bearer token123\r\n\r\n{\r\n  ""name"": ""测试用户"",\r\n  ""email"": ""<EMAIL>"",\r\n  ""data"": {\r\n    ""key1"": ""value1"",\r\n    ""key2"": ""value2""\r\n  }\r\n}"'''
    
    print("测试场景2：复杂的多行HTTP负载")
    print("解析结果:")
    complex_rows = parse_csv_correctly(test_case_2)
    for i, row in enumerate(complex_rows, 1):
        if len(row['data']) >= 2:
            print(f"  测试{i}: {row['data'][0]}")
            payload = row['data'][1]
            print(f"    HTTP负载长度: {len(payload)} 字符")
            crlf_count = payload.count('\\r\\n')
            print(f"    包含\\r\\n数量: {crlf_count}")
            print(f"    负载预览: {payload[:100]}...")
    print()
    
    # 测试场景3：边界情况
    test_case_3 = '''测试项目名称,HTTP负载,备注
"引号测试","GET /test HTTP/1.1\r\nHost: ""quoted-host"".com\r\n\r\n","包含引号的主机名"
"逗号测试","POST /data HTTP/1.1\r\nHost: example.com\r\n\r\n{""message"": ""Hello, World!""}","负载中包含逗号"
"空行测试","GET /empty HTTP/1.1\r\nHost: example.com\r\n\r\n\r\n","负载末尾有空行"'''
    
    print("测试场景3：边界情况")
    print("解析结果:")
    edge_rows = parse_csv_correctly(test_case_3)
    for i, row in enumerate(edge_rows, 1):
        print(f"  行{i}: {len(row['data'])} 列")
        if len(row['data']) >= 2:
            print(f"    测试名称: {row['data'][0]}")
            print(f"    HTTP负载: {repr(row['data'][1][:50])}...")
            if len(row['data']) >= 3:
                print(f"    备注: {row['data'][2]}")
    print()

def parse_csv_correctly(text):
    """
    正确的CSV解析实现 - 处理引号内的换行符
    这是JavaScript版本的Python实现
    """
    result = []
    current_line = ''
    in_quotes = False
    row_index = 0
    
    i = 0
    while i < len(text):
        char = text[i]
        next_char = text[i + 1] if i + 1 < len(text) else ''
        
        if char == '"':
            if in_quotes and next_char == '"':
                # 转义的引号 ""
                current_line += '""'
                i += 1  # 跳过下一个引号
            else:
                # 切换引号状态
                in_quotes = not in_quotes
                current_line += char
        elif (char == '\n' or char == '\r') and not in_quotes:
            # 只有在引号外的换行符才是真正的行分隔符
            if current_line.strip():
                row_index += 1
                row = parse_csv_line(current_line.strip())
                result.append({
                    'rowIndex': row_index,
                    'data': row,
                    'rawLine': current_line.strip()
                })
                current_line = ''
            # 跳过 \r\n 中的 \n
            if char == '\r' and next_char == '\n':
                i += 1
        elif char != '\r' or in_quotes:
            # 添加字符到当前行（引号外的\r被忽略，引号内的\r保留）
            current_line += char
        
        i += 1
    
    # 处理最后一行
    if current_line.strip():
        row_index += 1
        row = parse_csv_line(current_line.strip())
        result.append({
            'rowIndex': row_index,
            'data': row,
            'rawLine': current_line.strip()
        })
    
    return result

def parse_csv_line(line):
    """解析CSV行"""
    result = []
    current = ''
    in_quotes = False
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"':
            if in_quotes and i + 1 < len(line) and line[i + 1] == '"':
                # 转义的引号
                current += '"'
                i += 1  # 跳过下一个引号
            else:
                # 切换引号状态
                in_quotes = not in_quotes
        elif char == ',' and not in_quotes:
            # 字段分隔符
            result.append(current.strip())
            current = ''
        else:
            current += char
        
        i += 1
    
    # 添加最后一个字段
    result.append(current.strip())
    
    return result

def test_performance_comparison():
    """测试性能对比"""
    print("=== 性能对比测试 ===")
    print()
    
    # 创建大量测试数据
    large_csv = "测试项目名称,HTTP负载\n"
    for i in range(1000):
        large_csv += f'"测试{i}","GET /api/test{i} HTTP/1.1\\r\\nHost: example{i}.com\\r\\nUser-Agent: TestAgent\\r\\n\\r\\n"\n'
    
    print(f"测试数据大小: {len(large_csv)} 字符")
    crlf_count = large_csv.count('\\r\\n')
    print(f"包含 {crlf_count} 个HTTP换行符")
    print()
    
    import time
    
    # 测试旧方法
    start_time = time.time()
    old_lines = large_csv.split('\n')
    old_time = time.time() - start_time
    print(f"旧方法 (简单split): {old_time:.4f}秒, 分割出 {len(old_lines)} 行")
    
    # 测试新方法
    start_time = time.time()
    new_rows = parse_csv_correctly(large_csv)
    new_time = time.time() - start_time
    print(f"新方法 (正确解析): {new_time:.4f}秒, 解析出 {len(new_rows)} 行")
    
    print(f"性能差异: {new_time/old_time:.2f}x")
    print(f"准确性提升: 从 {len(old_lines)} 行错误分割 → {len(new_rows)} 行正确解析")
    print()

def test_javascript_compatibility():
    """测试JavaScript兼容性"""
    print("=== JavaScript兼容性测试 ===")
    print()
    
    # 生成JavaScript测试代码
    js_test_code = '''
// 测试用的CSV数据
var testCsv = "测试项目名称,HTTP负载\\n" +
    "\\"GET请求\\",\\"GET /api HTTP/1.1\\\\r\\\\nHost: example.com\\\\r\\\\n\\\\r\\\\n\\"\\n" +
    "\\"POST请求\\",\\"POST /data HTTP/1.1\\\\r\\\\nContent-Type: json\\\\r\\\\n\\\\r\\\\n{data}\\"";

// 新的解析函数（ES5兼容）
function parseCsvText(text) {
    var result = [];
    var currentLine = '';
    var inQuotes = false;
    var rowIndex = 0;
    
    for (var i = 0; i < text.length; i++) {
        var char = text[i];
        var nextChar = i + 1 < text.length ? text[i + 1] : '';
        
        if (char === '"') {
            if (inQuotes && nextChar === '"') {
                currentLine += '""';
                i++;
            } else {
                inQuotes = !inQuotes;
                currentLine += char;
            }
        } else if ((char === '\\n' || char === '\\r') && !inQuotes) {
            if (currentLine.trim()) {
                rowIndex++;
                result.push({
                    rowIndex: rowIndex,
                    rawLine: currentLine.trim()
                });
                currentLine = '';
            }
            if (char === '\\r' && nextChar === '\\n') {
                i++;
            }
        } else if (char !== '\\r' || inQuotes) {
            currentLine += char;
        }
    }
    
    if (currentLine.trim()) {
        rowIndex++;
        result.push({
            rowIndex: rowIndex,
            rawLine: currentLine.trim()
        });
    }
    
    return result;
}

// 测试执行
var result = parseCsvText(testCsv);
console.log("解析结果:", result.length, "行");
'''
    
    print("生成的JavaScript测试代码:")
    print(js_test_code)
    print()
    print("✅ 代码特点:")
    print("- 使用 var 声明变量（ES5兼容）")
    print("- 使用普通函数声明（非箭头函数）")
    print("- 使用字符串拼接（非模板字符串）")
    print("- 使用 for 循环（非 forEach）")
    print("- 兼容旧版本浏览器")

if __name__ == '__main__':
    test_csv_parsing_scenarios()
    test_performance_comparison()
    test_javascript_compatibility()
